<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stop Button Test - Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
            max-width: 800px;
            margin: 0 auto;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            display: inline-block;
        }
        .stop-button {
            background: #dc3545;
        }
        .test-button {
            background: #ffc107;
            color: black;
        }
        .log {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.generating {
            background: #ffc107;
            color: black;
        }
        .status.stopped {
            background: #dc3545;
        }
        .status.idle {
            background: #28a745;
        }
        .chat-area {
            background: #333;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            min-height: 200px;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .user-message {
            background: #007bff;
            text-align: right;
        }
        .assistant-message {
            background: #28a745;
        }
        .typing-indicator {
            display: inline-block;
        }
        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #fff;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <h1>🛑 Stop Button Functionality Test</h1>
    
    <div id="status" class="status idle">Status: Ready for testing</div>
    
    <div class="controls">
        <button id="start-simulation" class="button">Start AI Simulation</button>
        <button id="stop-button" class="button stop-button" style="display: none;">
            ⏹️ Stop Generation
        </button>
        <button id="test-button" class="button test-button">Run Diagnostics</button>
        <button id="clear-log" class="button">Clear Log</button>
    </div>
    
    <div class="chat-area" id="chat-area">
        <div class="message user-message">Test message: Please generate a response</div>
    </div>
    
    <div class="log" id="log"></div>
    
    <h2>Test Instructions:</h2>
    <ol>
        <li><strong>Start AI Simulation:</strong> Click to begin a mock AI response generation</li>
        <li><strong>Test Stop Button:</strong> While simulation is running, click the red stop button</li>
        <li><strong>Verify Behavior:</strong> Check that generation stops immediately</li>
        <li><strong>Run Diagnostics:</strong> Click to check all components are working</li>
    </ol>

    <script>
        const startButton = document.getElementById('start-simulation');
        const stopButton = document.getElementById('stop-button');
        const testButton = document.getElementById('test-button');
        const clearButton = document.getElementById('clear-log');
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        const chatArea = document.getElementById('chat-area');
        
        let abortController = null;
        let isGenerating = false;
        let simulationInterval = null;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(status, className) {
            statusDiv.textContent = `Status: ${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        function addMessage(content, isAssistant = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isAssistant ? 'assistant-message' : 'user-message'}`;
            messageDiv.innerHTML = content;
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
            return messageDiv;
        }
        
        async function startSimulation() {
            if (isGenerating) {
                log('❌ Simulation already running');
                return;
            }
            
            log('🚀 Starting AI generation simulation');
            isGenerating = true;
            updateStatus('Generating AI response...', 'generating');
            
            // Show stop button
            stopButton.style.display = 'inline-block';
            startButton.disabled = true;
            
            // Create abort controller
            abortController = new AbortController();
            log('✅ AbortController created');
            log('✅ Stop button shown');
            
            // Add typing indicator
            const messageDiv = addMessage('<div class="typing-indicator"><span></span><span></span><span></span></div>', true);
            
            try {
                // Simulate streaming response
                const words = ['This', 'is', 'a', 'simulated', 'AI', 'response', 'to', 'test', 'the', 'stop', 'button', 'functionality.', 'The', 'generation', 'should', 'stop', 'immediately', 'when', 'you', 'click', 'the', 'stop', 'button.', 'If', 'this', 'continues', 'without', 'stopping,', 'there', 'is', 'an', 'issue', 'with', 'the', 'implementation.'];
                let text = '';
                
                for (let i = 0; i < words.length; i++) {
                    // Check if aborted
                    if (abortController.signal.aborted) {
                        log('🛑 Generation aborted by user');
                        throw new DOMException('The operation was aborted.', 'AbortError');
                    }
                    
                    text += (i > 0 ? ' ' : '') + words[i];
                    messageDiv.innerHTML = text + '<span style="opacity: 0.5;">|</span>';
                    log(`📝 Generated word ${i + 1}/${words.length}: "${words[i]}"`);
                    
                    // Wait to simulate network delay
                    await new Promise(resolve => setTimeout(resolve, 300));
                }
                
                messageDiv.innerHTML = text;
                log('✅ Generation completed successfully');
                updateStatus('Generation completed', 'idle');
                
            } catch (error) {
                if (error.name === 'AbortError') {
                    messageDiv.innerHTML = text + ' <em style="color: #ffc107;">[Stopped by user]</em>';
                    log('✅ Generation stopped by user (expected behavior)');
                    updateStatus('Stopped by user', 'stopped');
                } else {
                    log(`❌ Error: ${error.message}`);
                    updateStatus('Error occurred', 'stopped');
                }
            } finally {
                // Clean up
                stopButton.style.display = 'none';
                startButton.disabled = false;
                abortController = null;
                isGenerating = false;
                log('🧹 Cleanup completed');
            }
        }
        
        function stopGeneration() {
            log('🛑 Stop button clicked');
            
            if (abortController) {
                log('⚡ Aborting generation...');
                abortController.abort();
            } else {
                log('⚠️ No active AbortController found');
            }
        }
        
        function runDiagnostics() {
            log('🔍 === RUNNING DIAGNOSTICS ===');
            
            // Test 1: DOM elements
            log('1️⃣ Checking DOM elements...');
            log(`   Stop button exists: ${!!stopButton}`);
            log(`   Start button exists: ${!!startButton}`);
            log(`   Log div exists: ${!!logDiv}`);
            
            // Test 2: Event listeners
            log('2️⃣ Checking event listeners...');
            log(`   Stop button onclick: ${typeof stopButton.onclick}`);
            
            // Test 3: AbortController support
            log('3️⃣ Checking AbortController support...');
            try {
                const testController = new AbortController();
                log(`   AbortController supported: ✅`);
                log(`   Signal object: ${!!testController.signal}`);
                log(`   Abort method: ${typeof testController.abort}`);
            } catch (e) {
                log(`   AbortController error: ❌ ${e.message}`);
            }
            
            // Test 4: Stop button visibility
            log('4️⃣ Testing stop button visibility...');
            const originalDisplay = stopButton.style.display;
            stopButton.style.display = 'inline-block';
            const computedStyle = window.getComputedStyle(stopButton);
            log(`   Display after setting inline-block: ${computedStyle.display}`);
            stopButton.style.display = 'none';
            log(`   Display after setting none: ${window.getComputedStyle(stopButton).display}`);
            stopButton.style.display = originalDisplay;
            
            log('✅ Diagnostics completed');
        }
        
        // Event listeners
        startButton.addEventListener('click', startSimulation);
        stopButton.addEventListener('click', stopGeneration);
        testButton.addEventListener('click', runDiagnostics);
        clearButton.addEventListener('click', () => {
            logDiv.textContent = '';
            log('Log cleared');
        });
        
        // Also add touch events for mobile
        stopButton.addEventListener('touchstart', function(e) {
            e.preventDefault();
            stopGeneration();
        });
        
        // Initialize
        log('🚀 Stop button test page loaded');
        log('📱 Ready for testing');
        
        // Auto-run diagnostics
        setTimeout(runDiagnostics, 1000);
    </script>
</body>
</html>
