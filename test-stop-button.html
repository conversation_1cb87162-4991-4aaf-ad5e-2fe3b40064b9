<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stop Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .stop-button {
            background: #dc3545;
        }
        .log {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.generating {
            background: #ffc107;
            color: black;
        }
        .status.stopped {
            background: #dc3545;
        }
        .status.idle {
            background: #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Stop Button Functionality Test</h1>
        
        <div id="status" class="status idle">Status: Idle</div>
        
        <button id="start-test" class="button">Start Simulated AI Generation</button>
        <button id="stop-button" class="button stop-button" style="display: none;">Stop Generation</button>
        
        <div class="log" id="log"></div>
        
        <h2>Instructions:</h2>
        <ol>
            <li>Click "Start Simulated AI Generation" to begin a mock streaming response</li>
            <li>While the generation is running, click the "Stop Generation" button</li>
            <li>Verify that the generation stops immediately</li>
            <li>Check the log for any error messages</li>
        </ol>
    </div>

    <script>
        const startButton = document.getElementById('start-test');
        const stopButton = document.getElementById('stop-button');
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        
        let abortController = null;
        let isGenerating = false;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(status, className) {
            statusDiv.textContent = `Status: ${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        async function simulateAIGeneration() {
            if (isGenerating) {
                log('Generation already in progress');
                return;
            }
            
            isGenerating = true;
            updateStatus('Generating...', 'generating');
            
            // Show stop button
            stopButton.style.display = 'inline-block';
            startButton.disabled = true;
            
            // Create abort controller
            abortController = new AbortController();
            log('Started AI generation simulation');
            log('AbortController created');
            
            try {
                // Simulate streaming response
                for (let i = 0; i < 100; i++) {
                    // Check if aborted
                    if (abortController.signal.aborted) {
                        log('Generation aborted by user');
                        throw new DOMException('The operation was aborted.', 'AbortError');
                    }
                    
                    // Simulate chunk of text
                    log(`Generated chunk ${i + 1}/100: "This is simulated AI text..."`);
                    
                    // Wait a bit to simulate network delay
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
                
                log('Generation completed successfully');
                updateStatus('Completed', 'idle');
                
            } catch (error) {
                if (error.name === 'AbortError') {
                    log('✅ Generation stopped by user (this is expected behavior)');
                    updateStatus('Stopped by user', 'stopped');
                } else {
                    log(`❌ Error: ${error.message}`);
                    updateStatus('Error', 'stopped');
                }
            } finally {
                // Clean up
                stopButton.style.display = 'none';
                startButton.disabled = false;
                abortController = null;
                isGenerating = false;
                log('Cleanup completed');
            }
        }
        
        function stopGeneration() {
            log('🛑 Stop button clicked');
            
            if (abortController) {
                log('Aborting generation...');
                abortController.abort();
                abortController = null;
            } else {
                log('⚠️ No active AbortController found');
            }
        }
        
        // Event listeners
        startButton.addEventListener('click', simulateAIGeneration);
        stopButton.addEventListener('click', stopGeneration);
        
        // Also add touch event for mobile
        stopButton.addEventListener('touchstart', function(e) {
            e.preventDefault();
            stopGeneration();
        });
        
        log('Test page loaded and ready');
        updateStatus('Ready for testing', 'idle');
    </script>
</body>
</html>
