// Ollama API configuration
const OLLAMA_HOST = 'http://localhost:11434';

// DOM elements
const modelSelect = document.getElementById('model-select');
const chatDisplay = document.getElementById('chat-display');
const chatMessages = document.getElementById('chat-messages');
const messageInput = document.getElementById('message-input');
const sendButton = document.getElementById('send-button');
const stopButton = document.getElementById('stop-button');
const fileInput = document.getElementById('file-input');
const attachButton = document.getElementById('attach-button');
const filePreview = document.getElementById('file-preview');
const newChatButton = document.getElementById('new-chat-button');
const chatHistoryContainer = document.getElementById('chat-history');
const themeToggle = document.getElementById('theme-toggle');

// Debug DOM elements
console.log('🔍 DOM Elements Check:');
console.log('  stopButton:', stopButton);
console.log('  sendButton:', sendButton);
console.log('  messageInput:', messageInput);
if (!stopButton) {
    console.error('❌ CRITICAL: Stop button element not found!');
} else {
    console.log('✅ Stop button found:', stopButton.id, stopButton.className);
}
const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
const sidebar = document.querySelector('.sidebar');

// Code editor elements
const codeEditorToggle = document.getElementById('code-editor-toggle');
const codeEditorPanel = document.getElementById('code-editor-panel');
const chatEditorResize = document.getElementById('chat-editor-resize');
const languageSelect = document.getElementById('language-select');
const runCodeButton = document.getElementById('run-code-button');
const shareToChatButton = document.getElementById('share-to-chat-button');
const toggleLayoutButton = document.getElementById('toggle-layout-button');
const fullscreenPreviewButton = document.getElementById('fullscreen-preview-button');
const copyCodeButton = document.getElementById('copy-code-button');
const downloadCodeButton = document.getElementById('download-code-button');
const codeTabButton = document.getElementById('code-tab-button');
const previewTabButton = document.getElementById('preview-tab-button');
const closeEditorButton = document.getElementById('close-editor-button');
const outputContent = document.getElementById('output-content');
const clearOutputButton = document.getElementById('clear-output-button');
const codeEditorTextarea = document.getElementById('code-editor');
const resizeHandle = document.getElementById('resize-handle');

// Controller for aborting fetch requests
let abortController = null;
let currentAttachments = [];

// Current chat state
let currentModel = '';
let isDarkTheme = true;

// NEW CHAT HISTORY SYSTEM - Simple and Reliable
let currentChatSession = {
    id: null,
    messages: [],
    model: '',
    title: '',
    timestamp: null
};

let allChatSessions = new Map(); // Use Map for better performance and reliability
const CHAT_STORAGE_KEY = 'ai_chat_sessions_v2'; // New storage key to avoid conflicts

// Code editor state
let codeEditor = null;
let isEditorVisible = false;
let currentLanguage = 'javascript';
let isHorizontalLayout = true; // Set horizontal layout as the default
let isFullscreenPreview = false;
let isResizing = false;
let isChatEditorResizing = false;
let lastEditorHeight = 0;
let lastPreviewHeight = 0;
let lastChatWidth = 0;
let lastEditorWidth = 0;

// Check localStorage usage
function checkStorageUsage() {
    try {
        let totalSize = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                totalSize += localStorage[key].length;
            }
        }

        const usageMB = (totalSize / (1024 * 1024)).toFixed(2);
        console.log(`LocalStorage usage: ${usageMB}MB`);

        // Warn if approaching limit
        if (totalSize > 4 * 1024 * 1024) { // 4MB warning threshold
            console.warn('LocalStorage approaching limit, consider cleanup');
        }

        return totalSize;
    } catch (error) {
        console.error('Error checking storage usage:', error);
        return 0;
    }
}

// NEW CHAT HISTORY SYSTEM - Initialize chat sessions from localStorage
function initializeChatSessions() {
    console.log('🔄 Initializing chat sessions...');

    try {
        // Check storage usage on load
        checkStorageUsage();

        // Load existing chat sessions
        const savedSessions = localStorage.getItem(CHAT_STORAGE_KEY);
        if (savedSessions) {
            const sessionsData = JSON.parse(savedSessions);
            allChatSessions.clear();

            // Convert array back to Map
            if (Array.isArray(sessionsData)) {
                sessionsData.forEach(session => {
                    if (session && session.id) {
                        allChatSessions.set(session.id, session);
                    }
                });
            }
        }

        // Create a new chat session if none exists
        if (allChatSessions.size === 0) {
            createNewChatSession();
        } else {
            // Load the most recent chat session
            const sessions = Array.from(allChatSessions.values());
            const mostRecent = sessions.sort((a, b) => b.timestamp - a.timestamp)[0];
            loadChatSession(mostRecent.id);
        }

        renderChatSessionsList();
        console.log('✅ Chat sessions initialized successfully');
    } catch (error) {
        console.error('❌ Error initializing chat sessions:', error);
        // Fallback: create a new session
        createNewChatSession();
    }
}

// Handle storage quota exceeded error
function handleStorageQuotaExceeded() {
    addMessage('system', '⚠️ Storage quota exceeded. Cleaning up old chats to free space...');

    try {
        cleanupOldChats();
        // Try saving again after cleanup
        saveChatSession();
        addMessage('system', '✅ Storage cleaned up successfully. Chat saved.');
    } catch (error) {
        console.error('Failed to cleanup storage:', error);
        addMessage('system', '❌ Failed to free up storage space. Please manually delete some old chats.');
    }
}

// NEW CHAT HISTORY SYSTEM - Create a new chat session
function createNewChatSession() {
    console.log('📝 Creating new chat session...');

    try {
        // Save current session if it has messages
        if (currentChatSession.id && currentChatSession.messages.length > 0) {
            saveChatSession();
        }

        // Create new session
        const newSessionId = 'chat_' + Date.now() + '_' + Math.random().toString(36).substring(2, 8);

        currentChatSession = {
            id: newSessionId,
            messages: [],
            model: currentModel || '',
            title: 'New Chat',
            timestamp: Date.now()
        };

        // Clear the display
        clearChatDisplay();

        // Clear attachments
        currentAttachments = [];
        if (filePreview) {
            filePreview.innerHTML = '';
            filePreview.style.display = 'none';
        }

        // Clear message input
        if (messageInput) {
            messageInput.value = '';
            messageInput.style.height = 'auto';
        }

        // Close all open panels when creating new chat to prevent blocking
        closeAllPanels();

        // Update UI
        renderChatSessionsList();

        console.log('✅ New chat session created:', newSessionId);
        return newSessionId;
    } catch (error) {
        console.error('❌ Error creating new chat session:', error);
        throw error;
    }
}

// Generate intelligent chat title from conversation content
function generateChatTitle(history) {
    if (!history || history.length === 0) {
        return 'New Chat';
    }

    // Find the first user message (skip system messages)
    const firstUserMessage = history.find(msg => msg.role === 'user');
    if (!firstUserMessage) {
        return 'New Chat';
    }

    let title = firstUserMessage.content.trim();

    // Remove code blocks and markdown formatting for cleaner titles
    title = title.replace(/```[\s\S]*?```/g, '[code]');
    title = title.replace(/`[^`]+`/g, '[code]');
    title = title.replace(/\*\*([^*]+)\*\*/g, '$1');
    title = title.replace(/\*([^*]+)\*/g, '$1');
    title = title.replace(/#{1,6}\s+/g, '');

    // Truncate to reasonable length and add ellipsis if needed
    if (title.length > 50) {
        title = title.substring(0, 47) + '...';
    }

    // If title is still too generic or empty, create a descriptive one
    if (!title || title.length < 3 || title.toLowerCase().match(/^(hi|hello|hey|test|help)\.?$/)) {
        const timestamp = new Date().toLocaleDateString();
        title = `Chat ${timestamp}`;
    }

    return title;
}

// NEW CHAT HISTORY SYSTEM - Save current chat session
function saveChatSession() {
    if (!currentChatSession.id) {
        console.warn('⚠️ No current chat session to save');
        return;
    }

    try {
        console.log('💾 Saving chat session:', currentChatSession.id);

        // Update session data
        currentChatSession.timestamp = Date.now();
        currentChatSession.model = currentModel;

        // Generate title if not set or if it's still "New Chat"
        if (!currentChatSession.title || currentChatSession.title === 'New Chat') {
            currentChatSession.title = generateChatTitle(currentChatSession.messages);
        }

        // Store in the sessions map
        allChatSessions.set(currentChatSession.id, { ...currentChatSession });

        // Save to localStorage
        const sessionsArray = Array.from(allChatSessions.values());
        localStorage.setItem(CHAT_STORAGE_KEY, JSON.stringify(sessionsArray));

        // Update UI
        renderChatSessionsList();

        console.log('✅ Chat session saved successfully');
    } catch (error) {
        console.error('❌ Error saving chat session:', error);
        if (error.name === 'QuotaExceededError') {
            handleStorageQuotaExceeded();
        }
    }
}

// NEW CHAT HISTORY SYSTEM - Clear chat display
function clearChatDisplay() {
    console.log('🧹 Clearing chat display...');

    // Clear the chat messages container
    if (chatMessages) {
        chatMessages.innerHTML = '';
        console.log('✅ Chat messages cleared');
    }

    // Also clear any direct children of chatDisplay that might be stuck
    if (chatDisplay) {
        // Remove any message containers that might be directly attached to chatDisplay
        const directMessages = chatDisplay.querySelectorAll('.message-container');
        directMessages.forEach(msg => {
            if (msg.parentElement === chatDisplay) {
                msg.remove();
                console.log('🗑️ Removed stuck message from chatDisplay');
            }
        });

        // Ensure we have the chat messages container
        const existingMessages = chatDisplay.querySelector('#chat-messages');
        if (!existingMessages) {
            chatDisplay.innerHTML = '<div id="chat-messages" class="chat-messages"></div>';
            // Re-get the reference
            const newChatMessages = document.getElementById('chat-messages');
            if (newChatMessages) {
                // Update the global reference
                window.chatMessages = newChatMessages;
                console.log('🔄 Chat messages container recreated');
            }
        }
    }

    console.log('✅ Chat display cleared completely');
}

// NEW CHAT HISTORY SYSTEM - Load a specific chat session
function loadChatSession(sessionId) {
    console.log('📂 Loading chat session:', sessionId);

    try {
        const session = allChatSessions.get(sessionId);
        if (!session) {
            console.error('❌ Chat session not found:', sessionId);
            return;
        }

        // Save current session if it has messages and is different
        if (currentChatSession.id &&
            currentChatSession.id !== sessionId &&
            currentChatSession.messages.length > 0) {
            saveChatSession();
        }

        // Load the session
        currentChatSession = { ...session };
        currentModel = session.model || '';

        // Update model select
        if (modelSelect && currentModel) {
            modelSelect.value = currentModel;
        }

        // Clear display and render messages
        clearChatDisplay();

        // Render all messages
        session.messages.forEach(msg => {
            if (msg.role !== 'system') {
                addMessage(msg.role, msg.content, true); // skipSave=true
            }
        });

        // Close all open panels when switching chats to prevent blocking
        closeAllPanels();

        // Update UI
        renderChatSessionsList();
        closeMobileSidebar();

        // Scroll to bottom
        setTimeout(() => {
            if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }, 100);

        console.log(`✅ Chat session loaded: ${sessionId} with ${session.messages.length} messages`);
    } catch (error) {
        console.error('❌ Error loading chat session:', error);
    }
}

// NEW CHAT HISTORY SYSTEM - Delete a chat session
function deleteChatSession(sessionId) {
    if (!confirm('Are you sure you want to delete this chat?')) {
        return;
    }

    try {
        console.log('🗑️ Deleting chat session:', sessionId);

        // Remove from sessions map
        allChatSessions.delete(sessionId);

        // Save to localStorage
        const sessionsArray = Array.from(allChatSessions.values());
        localStorage.setItem(CHAT_STORAGE_KEY, JSON.stringify(sessionsArray));

        // If we deleted the current session, create a new one
        if (sessionId === currentChatSession.id) {
            createNewChatSession();
        } else {
            // Otherwise just re-render the list
            renderChatSessionsList();
        }

        console.log('✅ Chat session deleted successfully');
    } catch (error) {
        console.error('❌ Error deleting chat session:', error);
    }
}

// NEW CHAT HISTORY SYSTEM - Render chat sessions list
function renderChatSessionsList() {
    if (!chatHistoryContainer) {
        console.warn('⚠️ Chat history container not found');
        return;
    }

    try {
        console.log('🎨 Rendering chat sessions list...');
        chatHistoryContainer.innerHTML = '';

        // Get all sessions and sort by timestamp (newest first)
        const sessions = Array.from(allChatSessions.values())
            .sort((a, b) => b.timestamp - a.timestamp);

        if (sessions.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-history-message';
            emptyMessage.textContent = 'No chat history yet';
            emptyMessage.style.cssText = 'padding: 20px; text-align: center; color: #666; font-style: italic;';
            chatHistoryContainer.appendChild(emptyMessage);
            return;
        }

        sessions.forEach(session => {
            const chatItem = document.createElement('div');
            chatItem.classList.add('chat-item');

            // Mark as active if it's the current session
            if (session.id === currentChatSession.id) {
                chatItem.classList.add('active');
            }

            // Chat icon
            const chatIcon = document.createElement('img');
            chatIcon.src = 'icons/history_chat_icon.svg';
            chatIcon.alt = 'Chat';
            chatIcon.style.cssText = 'width: 16px; height: 16px; margin-right: 8px; filter: brightness(0) saturate(100%) invert(56%) sepia(8%) saturate(378%) hue-rotate(202deg) brightness(95%) contrast(86%);';

            // Title
            const titleSpan = document.createElement('span');
            titleSpan.textContent = session.title || 'Untitled Chat';
            titleSpan.style.cssText = 'flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;';

            // Click handler to load session
            chatItem.addEventListener('click', () => loadChatSession(session.id));

            // Delete button
            const deleteButton = document.createElement('button');
            deleteButton.classList.add('delete-chat-button');
            deleteButton.innerHTML = '<img src="icons/history_delete_icon.svg" alt="Delete" style="width: 14px; height: 14px; filter: brightness(0) saturate(100%) invert(56%) sepia(8%) saturate(378%) hue-rotate(202deg) brightness(95%) contrast(86%);">';
            deleteButton.title = 'Delete chat';
            deleteButton.addEventListener('click', (e) => {
                e.stopPropagation();
                deleteChatSession(session.id);
            });

            chatItem.appendChild(chatIcon);
            chatItem.appendChild(titleSpan);
            chatItem.appendChild(deleteButton);
            chatHistoryContainer.appendChild(chatItem);
        });

        console.log(`✅ Rendered ${sessions.length} chat sessions`);
    } catch (error) {
        console.error('❌ Error rendering chat sessions list:', error);
    }
}

// Configure marked.js with highlight.js
marked.setOptions({
    highlight: function(code, lang) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext';
        const result = hljs.highlight(code, { language });
        console.log('Highlighting code:', {lang, language});
        return result.value;
    },
    langPrefix: 'hljs language-'
});

// Initialize the app
async function init() {
    // Always setup event listeners first
    setupEventListeners();

    // Initialize feature managers
    initializeFeatureManagers();

    // Check if running from file:// protocol
    if (window.location.protocol === 'file:') {
        // Add demo messages to show the design
        addMessage('user', 'Hi.', true);
        addMessage('assistant', `Hi there! How can I help you today? 😊

Do you want to:
• Chat about something?
• Ask me a question?
• Play a game?
• Get some information?

Just let me know what you're thinking!`, true);
        return;
    }

    try {
        await loadModels();
    } catch (error) {
        console.error('Initialization error:', error);
    }
}

// Initialize feature managers and their event listeners
function initializeFeatureManagers() {
    console.log('🚀 Initializing feature managers...');

    // Initialize AI Provider Manager first
    if (typeof AIProviderManager !== 'undefined' && !window.aiProviderManager) {
        window.aiProviderManager = new AIProviderManager();
        console.log('✅ AI Provider Manager initialized');
    }

    // Initialize AI Persona Manager
    if (typeof AIPersonaManager !== 'undefined' && !window.aiPersonaManager) {
        window.aiPersonaManager = new AIPersonaManager();
        // Load settings immediately after initialization
        window.aiPersonaManager.loadSettings();
        console.log('✅ AI Persona Manager initialized');

        // Load models for the current provider
        setTimeout(() => {
            loadModels();
        }, 200);
    }

    // Initialize Export/Import Manager
    if (typeof ExportImportManager !== 'undefined' && !window.exportImportManager) {
        window.exportImportManager = new ExportImportManager();
        console.log('✅ Export/Import Manager initialized');
    }

    // Initialize Code Integration Manager
    if (typeof CodeIntegrationManager !== 'undefined' && !window.codeIntegrationManager) {
        window.codeIntegrationManager = new CodeIntegrationManager();
        console.log('✅ Code Integration Manager initialized');
    }

    // Set up module-specific event listeners with a delay to ensure DOM is ready
    setTimeout(() => {
        setupModuleEventListeners();

        // Add fallback event listener for AI Persona button if it's still not working
        addFallbackPersonaListener();
    }, 100);
}

// Fallback method for AI Persona button if main method fails
function addFallbackPersonaListener() {
    console.log('🔧 Setting up fallback persona button listener...');

    const personaButton = document.getElementById('ai-persona-toggle');
    const personaPanel = document.getElementById('ai-persona-panel');

    if (personaButton && personaPanel) {
        // Use direct onclick assignment as fallback
        personaButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🎯 FALLBACK: Persona button clicked!');

            // Close export panel if open
            const exportPanel = document.getElementById('export-import-panel');
            if (exportPanel && exportPanel.classList.contains('open')) {
                exportPanel.classList.remove('open');
            }

            // Toggle persona panel
            personaPanel.classList.toggle('open');
            console.log('👤 FALLBACK: Persona panel toggled');

            // Initialize AI Persona Manager if not already done
            if (!window.aiPersonaManager && typeof AIPersonaManager !== 'undefined') {
                window.aiPersonaManager = new AIPersonaManager();
                window.aiPersonaManager.loadSettings();
                console.log('🚀 FALLBACK: AI Persona Manager initialized');
            }

            return false;
        };
        console.log('✅ Fallback persona button listener added');
    } else {
        console.error('❌ FALLBACK: Persona button or panel still not found');
    }
}

// Fetch available models for the current provider
async function loadModels() {
    if (!modelSelect) {
        console.log('Model select element not found, skipping model loading');
        return;
    }

    const currentProvider = window.aiPersonaManager?.currentProvider || 'ollama';

    try {
        const models = await window.aiProviderManager.loadModelsForProvider(currentProvider);

        modelSelect.innerHTML = '';
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select a model...';
        modelSelect.appendChild(defaultOption);

        models.forEach(modelName => {
            const option = document.createElement('option');
            option.value = modelName;
            option.textContent = modelName;
            modelSelect.appendChild(option);
        });

        // Remove existing event listener to avoid duplicates
        modelSelect.removeEventListener('change', handleModelChange);
        modelSelect.addEventListener('change', handleModelChange);

    } catch (error) {
        console.error(`Error loading models for ${currentProvider}:`, error);

        if (currentProvider === 'ollama') {
            addMessage('system', `Failed to load Ollama models. Please ensure the following:`);
            addMessage('system', `1. Ollama is installed: Download from https://ollama.ai/download`);
            addMessage('system', `2. Ollama is running: Ensure the service is running in the background`);
            addMessage('system', `3. Model is downloaded: Run \`ollama pull mistral\` to download a model`);
            addMessage('system', `4. Accessible at http://localhost:11434`);
        } else {
            addMessage('system', `Failed to load models for ${window.aiProviderManager.getProviderDisplayName(currentProvider)}. Please check your API key and connection.`);
        }
    }
}

function handleModelChange(e) {
    currentModel = e.target.value;
    if (currentModel) {
        const provider = window.aiPersonaManager?.currentProvider || 'ollama';
        const providerName = window.aiProviderManager.getProviderDisplayName(provider);
        addMessage('system', `Model changed to ${currentModel} (${providerName})`);
    }
}

// Theme management
function loadThemePreference() {
    const savedTheme = localStorage.getItem('theme-preference');
    if (savedTheme) {
        isDarkTheme = savedTheme === 'dark';
        applyTheme();
    } else {
        // Default to dark theme for mobile app
        isDarkTheme = true;
        applyTheme();
        // Save the default preference
        localStorage.setItem('theme-preference', 'dark');
    }
}

function toggleTheme() {
    isDarkTheme = !isDarkTheme;
    applyTheme();
    localStorage.setItem('theme-preference', isDarkTheme ? 'dark' : 'light');
}

function applyTheme() {
    const lightThemeLink = document.getElementById('highlight-light-theme');
    const darkThemeLink = document.getElementById('highlight-dark-theme');

    if (isDarkTheme) {
        // Apply dark theme
        document.body.classList.remove('light-theme');
        document.body.classList.add('dark-theme');
        themeToggle.innerHTML = '<img src="icons/light_mode.svg" alt="Light Mode" class="icon">';

        // Switch syntax highlighting theme
        lightThemeLink.disabled = true;
        darkThemeLink.disabled = false;
    } else {
        // Apply light theme
        document.body.classList.remove('dark-theme');
        document.body.classList.add('light-theme');
        themeToggle.innerHTML = '<img src="icons/dark_mode.svg" alt="Dark Mode" class="icon">';

        // Switch syntax highlighting theme
        lightThemeLink.disabled = false;
        darkThemeLink.disabled = true;
    }

    // Re-highlight code blocks with the new theme
    document.querySelectorAll('pre code').forEach(block => {
        hljs.highlightElement(block);
    });

    // Update code editor theme if it exists
    updateEditorTheme();
}

// Mobile sidebar toggle
function toggleSidebar() {
    console.log('Toggle sidebar called');
    if (sidebar) {
        sidebar.classList.toggle('open');
        console.log('Sidebar classes:', sidebar.className);
    } else {
        console.error('Sidebar element not found');
    }
}

// Helper function to close mobile sidebar
function closeMobileSidebar() {
    if (window.innerWidth <= 768 && sidebar && sidebar.classList.contains('open')) {
        sidebar.classList.remove('open');
        console.log('Mobile sidebar closed');
    }
}

// Helper function to close all open panels
function closeAllPanels() {
    console.log('🔒 Closing all open panels...');

    // Close AI Persona panel
    const personaPanel = document.getElementById('ai-persona-panel');
    if (personaPanel && personaPanel.classList.contains('open')) {
        personaPanel.classList.remove('open');
        console.log('👤 Closed AI Persona panel');
    }

    // Close Export/Import panel
    const exportPanel = document.getElementById('export-import-panel');
    if (exportPanel && exportPanel.classList.contains('open')) {
        exportPanel.classList.remove('open');
        console.log('📤 Closed Export/Import panel');
    }

    // Close Code Integration panel
    const codePanel = document.getElementById('code-integration-panel');
    if (codePanel && codePanel.style.display !== 'none') {
        codePanel.style.display = 'none';
        console.log('💻 Closed Code Integration panel');
    }

    // Close Attribution panel
    const attributionPanel = document.getElementById('attribution-panel');
    if (attributionPanel && attributionPanel.classList.contains('open')) {
        attributionPanel.classList.remove('open');
        console.log('ℹ️ Closed Attribution panel');
    }

    console.log('✅ All panels closed');
}

// Setup global click handler to close panels when clicking outside
function setupGlobalClickHandler() {
    document.addEventListener('click', function(event) {
        // Get all panel elements
        const personaPanel = document.getElementById('ai-persona-panel');
        const exportPanel = document.getElementById('export-import-panel');
        const attributionPanel = document.getElementById('attribution-panel');

        // Check if click is outside any open panel

        // Check persona panel
        if (personaPanel && personaPanel.classList.contains('open')) {
            if (!personaPanel.contains(event.target) &&
                !event.target.closest('#ai-persona-toggle')) {
                personaPanel.classList.remove('open');
                console.log('👤 Closed AI Persona panel (clicked outside)');
            }
        }

        // Check export panel
        if (exportPanel && exportPanel.classList.contains('open')) {
            if (!exportPanel.contains(event.target) &&
                !event.target.closest('#export-import-toggle')) {
                exportPanel.classList.remove('open');
                console.log('📤 Closed Export/Import panel (clicked outside)');
            }
        }

        // Check attribution panel
        if (attributionPanel && attributionPanel.classList.contains('open')) {
            if (!attributionPanel.contains(event.target) &&
                !event.target.closest('#attribution-toggle')) {
                attributionPanel.classList.remove('open');
                console.log('ℹ️ Closed Attribution panel (clicked outside)');
            }
        }
    });

    console.log('✅ Global click handler for panels setup complete');
}

// NEW CHAT HISTORY SYSTEM - Reload/refresh chat functionality (preserves data like browser reload)
function reloadChat() {
    try {
        console.log('🔄 Reloading app (preserving data)...');

        // Close all panels first
        closeAllPanels();
        closeMobileSidebar();

        // Check if we're in Android WebView
        const isAndroidWebView = typeof AndroidAPI !== 'undefined';

        if (isAndroidWebView) {
            // For Android WebView, try to reload the WebView without clearing data
            console.log('📱 Android WebView detected - performing soft reload...');

            // Try to use Android API to reload WebView (preserving data)
            if (AndroidAPI.reloadWebView) {
                AndroidAPI.reloadWebView();
                console.log('✅ WebView reload initiated');
                return;
            }

            // If no WebView reload available, try app restart (but this will clear data)
            if (window.restartApp && window.restartApp()) {
                console.log('⚠️ App restart initiated (data may be cleared)');
                return;
            }

            // Fallback: Soft reload by reinitializing without clearing storage
            console.log('🔄 Fallback: Soft reload without clearing data...');

            // Reset only UI state, NOT storage data
            currentAttachments = [];

            // Clear only UI elements
            if (chatMessages) chatMessages.innerHTML = '';
            if (messageInput) {
                messageInput.value = '';
                messageInput.style.height = 'auto';
            }
            if (filePreview) {
                filePreview.innerHTML = '';
                filePreview.style.display = 'none';
            }

            // Close any open panels
            closeAllPanels();

            // Reinitialize the app from saved data
            setTimeout(() => {
                // Reload from existing localStorage data
                initializeChatSessions();
                loadModels();

                // Load theme preference
                loadThemePreference();

                console.log('✅ App soft reloaded successfully');
                addMessage('system', '🔄 App reloaded successfully');
            }, 100);

        } else {
            // For web browsers, perform a standard reload (preserves localStorage)
            console.log('🌐 Web browser detected - performing standard reload...');

            // Standard browser reload (preserves localStorage and sessionStorage)
            if (window.location.reload) {
                window.location.reload(); // Standard reload
            } else {
                // Fallback method
                window.location.href = window.location.href;
            }
        }

        console.log('✅ Reload initiated successfully');
    } catch (error) {
        console.error('❌ Error during reload:', error);
        addMessage('system', '❌ Failed to reload app: ' + error.message);

        // Last resort fallback - standard reload
        try {
            window.location.reload();
        } catch (fallbackError) {
            console.error('❌ Fallback reload also failed:', fallbackError);
            addMessage('system', '❌ Please manually refresh the page');
        }
    }
}

// Initialize code editor
function initCodeEditor() {
    // Set default content for HTML+CSS mode
    if (currentLanguage === 'htmlcss' && !codeEditorTextarea.value) {
        codeEditorTextarea.value = `<!DOCTYPE html>
<html>
<head>
    <style>
        /* Your CSS here */
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hello, World!</h1>
        <p>This is a combined HTML and CSS editor.</p>
        <button>Click Me</button>
    </div>
</body>
</html>`;
    } else if (currentLanguage === 'htmlmixed' && !codeEditorTextarea.value) {
        codeEditorTextarea.value = `<!DOCTYPE html>
<html>
<head>
    <title>HTML Example</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>This is a sample HTML page.</p>
    <button>Click Me</button>
</body>
</html>`;
    } else if (currentLanguage === 'css' && !codeEditorTextarea.value) {
        codeEditorTextarea.value = `/* CSS Example */
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h1 {
    color: #0066cc;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

button {
    background-color: #0066cc;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background-color: #0055aa;
}`;
    } else if (currentLanguage === 'javascript' && !codeEditorTextarea.value) {
        codeEditorTextarea.value = `// JavaScript Example
function greet(name) {
    return "Hello, " + name + "!";
}

// Try calling the function
console.log(greet("World"));

// Or create an array and manipulate it
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log(doubled);`;
    }

    // Use htmlmixed mode for htmlcss
    const editorMode = currentLanguage === 'htmlcss' ? 'htmlmixed' : currentLanguage;

    codeEditor = CodeMirror.fromTextArea(codeEditorTextarea, {
        mode: editorMode,
        theme: isDarkTheme ? "material-darker" : "default",
        lineNumbers: true,
        autoCloseBrackets: true,
        matchBrackets: true,
        indentUnit: 4,
        tabSize: 4,
        lineWrapping: true,
        extraKeys: {"Ctrl-Space": "autocomplete"}
    });

    // Expose code editor globally for other modules
    window.codeEditor = codeEditor;

    // Make sure we start in code mode
    codeEditorPanel.classList.add('code-mode');
    codeEditorPanel.classList.remove('preview-mode');
    codeTabButton.classList.add('active');
    previewTabButton.classList.remove('active');

    // Update editor theme when app theme changes
    updateEditorTheme();
}

// Update editor theme based on app theme
function updateEditorTheme() {
    if (codeEditor) {
        codeEditor.setOption("theme", isDarkTheme ? "material-darker" : "default");
    }
}

// Toggle code editor visibility
function toggleCodeEditor() {
    isEditorVisible = !isEditorVisible;

    if (isEditorVisible) {
        // Show the code editor panel and resize handle
        codeEditorPanel.style.display = 'flex';

        // Check if we're on mobile
        const isMobile = window.innerWidth <= 768;

        // Add class to body for mobile input container styling
        if (isMobile) {
            document.body.classList.add('code-editor-open');
        }

        if (!isMobile) {
            chatEditorResize.style.display = 'block';
        }

        // Initialize editor if it doesn't exist
        if (!codeEditor) {
            initCodeEditor();
        } else {
            codeEditor.refresh(); // Refresh to ensure proper rendering
        }

        // Always use horizontal layout
        codeEditorPanel.classList.add('horizontal-layout');
        codeEditorPanel.classList.remove('vertical-layout');

        // Update the editor content flex direction
        const editorContent = document.querySelector('.editor-content');
        if (editorContent) {
            if (isMobile) {
                editorContent.style.flexDirection = 'column';
            } else {
                editorContent.style.flexDirection = 'row';
            }
        }

        // Make sure code mode is active by default
        codeEditorPanel.classList.add('code-mode');
        codeEditorPanel.classList.remove('preview-mode');
        codeTabButton.classList.add('active');
        previewTabButton.classList.remove('active');

        // Set up resize handles only for desktop
        if (!isMobile) {
            setupResizeHandlers();
            setupChatEditorResizeHandlers();

            // Set initial widths if not already set
            if (!lastChatWidth) {
                const mainContent = document.querySelector('.main-content');
                lastChatWidth = mainContent.offsetWidth * 0.6;
                lastEditorWidth = mainContent.offsetWidth * 0.4;

                document.getElementById('chat-display').style.width = `${lastChatWidth}px`;
                codeEditorPanel.style.width = `${lastEditorWidth}px`;
            }
        }

        // Apply the correct layout immediately
        setTimeout(() => {
            if (isHorizontalLayout && !isMobile) {
                const editorContainer = document.getElementById('editor-container');
                const previewContainer = document.getElementById('preview-container');
                const resizeHandle = document.getElementById('resize-handle');

                if (editorContainer && previewContainer) {
                    editorContainer.style.height = '100%';
                    previewContainer.style.height = '100%';
                    editorContainer.style.width = '60%';
                    previewContainer.style.width = '40%';
                }

                if (resizeHandle) {
                    resizeHandle.style.width = '6px';
                    resizeHandle.style.height = '100%';
                    resizeHandle.style.cursor = 'col-resize';
                }
            }

            // Refresh CodeMirror to ensure proper rendering
            if (codeEditor) {
                codeEditor.refresh();
            }
        }, 50);
    } else {
        // Hide the code editor panel and resize handle
        codeEditorPanel.style.display = 'none';
        chatEditorResize.style.display = 'none';

        // Remove class from body for mobile input container styling
        document.body.classList.remove('code-editor-open');

        // Reset chat display width only on desktop
        if (window.innerWidth > 768) {
            document.getElementById('chat-display').style.width = '';
        }
    }
}

// Change editor language
function changeEditorLanguage(language) {
    currentLanguage = language;

    if (codeEditor) {
        // Use htmlmixed mode for htmlcss
        const editorMode = language === 'htmlcss' ? 'htmlmixed' : language;
        codeEditor.setOption('mode', editorMode);

        // If editor is empty, provide template code
        if (codeEditor.getValue().trim() === '') {
            let templateCode = '';

            if (language === 'htmlcss') {
                templateCode = `<!DOCTYPE html>
<html>
<head>
    <style>
        /* Your CSS here */
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hello, World!</h1>
        <p>This is a combined HTML and CSS editor.</p>
        <button>Click Me</button>
    </div>
</body>
</html>`;
            } else if (language === 'htmlmixed') {
                templateCode = `<!DOCTYPE html>
<html>
<head>
    <title>HTML Example</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>This is a sample HTML page.</p>
    <button>Click Me</button>
</body>
</html>`;
            } else if (language === 'css') {
                templateCode = `/* CSS Example */
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h1 {
    color: #0066cc;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

button {
    background-color: #0066cc;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background-color: #0055aa;
}`;
            } else if (language === 'javascript') {
                templateCode = `// JavaScript Example
function greet(name) {
    return "Hello, " + name + "!";
}

// Try calling the function
console.log(greet("World"));

// Or create an array and manipulate it
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log(doubled);`;
            }

            if (templateCode) {
                codeEditor.setValue(templateCode);
            }
        }
    }
}

// Track last run code to prevent unnecessary refreshes
let lastRunCode = '';
let lastRunLanguage = '';
let lastRunTimestamp = 0;

// Run code in the editor
function runCode() {
    const code = codeEditor.getValue();
    const language = currentLanguage;
    const currentTime = Date.now();

    if (!code.trim()) {
        outputContent.textContent = 'No code to run';
        return;
    }

    // Prevent constant refreshing by checking if code has changed and if enough time has passed
    if (code === lastRunCode && language === lastRunLanguage && currentTime - lastRunTimestamp < 1000) {
        return;
    }

    // Update tracking variables
    lastRunCode = code;
    lastRunLanguage = language;
    lastRunTimestamp = currentTime;

    try {
        // Clear previous output
        outputContent.innerHTML = '';

        if (language === 'javascript') {
            outputContent.textContent = 'Running JavaScript...';

            // Create a sandbox for JavaScript execution
            const sandbox = function(code) {
                let console = {
                    log: function(...args) {
                        return args.map(arg =>
                            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                        ).join(' ');
                    },
                    error: function(...args) {
                        return "ERROR: " + args.map(arg =>
                            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                        ).join(' ');
                    },
                    warn: function(...args) {
                        return "WARNING: " + args.map(arg =>
                            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                        ).join(' ');
                    }
                };

                let output = [];
                const originalLog = console.log;
                const originalError = console.error;
                const originalWarn = console.warn;

                console.log = function(...args) {
                    output.push(originalLog.apply(console, args));
                };
                console.error = function(...args) {
                    output.push(originalError.apply(console, args));
                };
                console.warn = function(...args) {
                    output.push(originalWarn.apply(console, args));
                };

                try {
                    // Add a return statement if the code is a single expression
                    let result;
                    if (!code.includes(';') && !code.includes('return') && !code.includes('function')) {
                        result = eval('(' + code + ')');
                        if (result !== undefined) {
                            output.push(typeof result === 'object' ? JSON.stringify(result, null, 2) : result);
                        }
                    } else {
                        result = eval(code);
                        if (result !== undefined && !code.includes('console.log')) {
                            output.push(typeof result === 'object' ? JSON.stringify(result, null, 2) : result);
                        }
                    }
                    return output.join('\n');
                } catch (error) {
                    return "Error: " + error.message;
                }
            };

            // Execute the code in the sandbox
            const result = sandbox(code);
            outputContent.textContent = result;

        } else if (language === 'htmlmixed' || language === 'htmlcss') {
            // Create an iframe for HTML preview
            const previewFrame = document.createElement('iframe');
            previewFrame.style.width = '100%';
            previewFrame.style.height = '100%';
            previewFrame.style.border = 'none';
            previewFrame.style.backgroundColor = 'white';
            previewFrame.style.display = 'block';
            previewFrame.style.flex = '1';
            previewFrame.style.minHeight = '300px';
            previewFrame.id = 'preview-frame';

            // Add a preview header
            const previewHeader = document.createElement('div');
            previewHeader.className = 'preview-header';
            previewHeader.textContent = language === 'htmlcss' ? 'HTML+CSS Preview' : 'HTML Preview';

            // Clear previous content
            outputContent.innerHTML = '';

            // Create a container for the preview that takes full height
            const previewContainer = document.createElement('div');
            previewContainer.style.display = 'flex';
            previewContainer.style.flexDirection = 'column';
            previewContainer.style.height = '100%';
            previewContainer.style.flex = '1';

            // Add the elements to the container
            previewContainer.appendChild(previewHeader);
            previewContainer.appendChild(previewFrame);

            // Add the container to the output
            outputContent.appendChild(previewContainer);

            // Write HTML content to iframe
            setTimeout(() => {
                try {
                    const frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;

                    // Create a full HTML document if it doesn't have proper structure
                    let htmlContent = code;
                    if (!htmlContent.includes('<!DOCTYPE html>') && !htmlContent.includes('<html')) {
                        htmlContent = `<!DOCTYPE html><html><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><body>${htmlContent}</body></html>`;
                    }

                    // Modern approach to avoid document.write deprecation
                    frameDoc.open();
                    frameDoc.close(); // Close and re-open to clear the document

                    // Set the HTML content directly
                    previewFrame.srcdoc = htmlContent;
                } catch (error) {
                    console.error('Error rendering HTML preview:', error);
                }
            }, 100); // Small delay to ensure iframe is ready

        } else if (language === 'css') {
            // Add a preview header
            const previewHeader = document.createElement('div');
            previewHeader.className = 'preview-header';
            previewHeader.textContent = 'CSS Preview';

            // Create an iframe for CSS preview with some demo elements
            const previewFrame = document.createElement('iframe');
            previewFrame.style.width = '100%';
            previewFrame.style.height = '100%';
            previewFrame.style.border = 'none';
            previewFrame.style.backgroundColor = 'white';
            previewFrame.style.display = 'block';
            previewFrame.style.flex = '1';
            previewFrame.style.minHeight = '300px';
            previewFrame.id = 'preview-frame';

            // Clear previous content
            outputContent.innerHTML = '';

            // Create a container for the preview that takes full height
            const previewContainer = document.createElement('div');
            previewContainer.style.display = 'flex';
            previewContainer.style.flexDirection = 'column';
            previewContainer.style.height = '100%';
            previewContainer.style.flex = '1';

            // Add the elements to the container
            previewContainer.appendChild(previewHeader);
            previewContainer.appendChild(previewFrame);

            // Add the container to the output
            outputContent.appendChild(previewContainer);

            // Create HTML with demo elements and the user's CSS
            const demoHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <style>${code}</style>
                </head>
                <body>
                    <div class="container">
                        <h1>CSS Preview</h1>
                        <p>This is a paragraph to demonstrate your CSS.</p>
                        <button>Button Element</button>
                        <div class="box">This is a div with class "box"</div>
                        <ul>
                            <li>List item 1</li>
                            <li>List item 2</li>
                            <li>List item 3</li>
                        </ul>
                    </div>
                </body>
                </html>
            `;

            // Write content to iframe
            setTimeout(() => {
                try {
                    const frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;

                    // Modern approach to avoid document.write deprecation
                    frameDoc.open();
                    frameDoc.close(); // Close and re-open to clear the document

                    // Set the HTML content directly
                    previewFrame.srcdoc = demoHTML;
                } catch (error) {
                    console.error('Error rendering CSS preview:', error);
                }
            }, 100); // Small delay to ensure iframe is ready

        } else {
            // For other languages, we'll just show a message
            outputContent.textContent = `Code execution for ${language} is not supported in the browser.\n\nYou would need a backend service to run ${language} code.`;
        }
    } catch (error) {
        outputContent.textContent = "Error: " + error.message;
    }
}

// Share code to chat
function shareCodeToChat() {
    if (!codeEditor) return;

    // Check if there's a selection first
    let code = codeEditor.getSelection();

    // If no selection, use the entire code
    if (!code || !code.trim()) {
        code = codeEditor.getValue();
        if (!code.trim()) {
            addMessage('system', 'No code to share');
            return;
        }
    }

    // Format code with language for markdown
    let language = currentLanguage;

    // For HTML+CSS mode, use htmlmixed
    if (language === 'htmlcss') {
        language = 'html';
    } else if (language === 'htmlmixed') {
        language = 'html';
    }

    const formattedCode = '```' + language + '\n' + code + '\n```';

    // Add to message input
    messageInput.value = formattedCode;

    // Focus on input
    messageInput.focus();
}

// Copy code to clipboard
function copyCodeToClipboard() {
    if (!codeEditor) return;

    const code = codeEditor.getValue();
    if (!code.trim()) {
        addMessage('system', 'No code to copy');
        return;
    }

    // Use the Clipboard API to copy the code
    navigator.clipboard.writeText(code)
        .then(() => {
            // Visual feedback for successful copy
            copyCodeButton.classList.add('success');
            copyCodeButton.innerHTML = '<i class="fa-solid fa-check"></i> Copied!';

            setTimeout(() => {
                copyCodeButton.classList.remove('success');
                copyCodeButton.innerHTML = '<i class="fa-regular fa-copy"></i> Copy';
            }, 2000);
        })
        .catch(err => {
            console.error('Failed to copy code:', err);
            addMessage('system', 'Failed to copy code to clipboard');
        });
}

// Download code as a file
function downloadCode() {
    if (!codeEditor) return;

    const code = codeEditor.getValue();
    if (!code.trim()) {
        addMessage('system', 'No code to download');
        return;
    }

    // Determine the appropriate file extension based on the language
    let fileExtension = '.txt';
    let language = currentLanguage;

    switch (language) {
        case 'javascript':
            fileExtension = '.js';
            break;
        case 'python':
            fileExtension = '.py';
            break;
        case 'htmlmixed':
        case 'htmlcss':
            fileExtension = '.html';
            break;
        case 'css':
            fileExtension = '.css';
            break;
        case 'xml':
            fileExtension = '.xml';
            break;
        default:
            fileExtension = '.txt';
    }

    // Create a filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `code-${language}-${timestamp}${fileExtension}`;

    // Check if we're running in Android WebView
    if (typeof AndroidAPI !== 'undefined' && AndroidAPI.downloadFile) {
        // Use Android native download for WebView
        try {
            AndroidAPI.downloadFile(code, filename, 'text/plain');
            addMessage('system', `Code saved to Downloads: ${filename}`);
            return;
        } catch (error) {
            console.error('Android download failed, falling back to blob download:', error);
        }
    }

    // Fallback to blob download for web browsers
    try {
        const blob = new Blob([code], { type: 'text/plain' });
        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = filename;

        // Append to the body, click it, and remove it
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // Release the object URL
        setTimeout(() => URL.revokeObjectURL(downloadLink.href), 100);
        addMessage('system', `Code downloaded: ${filename}`);
    } catch (error) {
        console.error('Code download failed:', error);
        addMessage('system', 'Failed to download code. Please try again.');
    }
}

// Clear output
function clearOutput() {
    outputContent.textContent = '';
}

// Update preview based on current code
function updatePreview() {
    if (!codeEditor) return;

    // Only update if the preview is visible
    const previewContainer = document.getElementById('preview-container');
    if (previewContainer &&
        (previewContainer.style.display === 'none' ||
         previewContainer.style.visibility === 'hidden')) {
        return;
    }

    // Run the code to update the preview
    runCode();
}

// Setup resize handlers for the code editor
function setupResizeHandlers() {
    // Reset any previous event listeners
    resizeHandle.removeEventListener('mousedown', handleResizeStart);
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);

    // Add new event listeners
    resizeHandle.addEventListener('mousedown', handleResizeStart);
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);

    // Touch events for mobile
    resizeHandle.addEventListener('touchstart', handleTouchResizeStart, { passive: false });
    document.addEventListener('touchmove', handleTouchResizeMove, { passive: false });
    document.addEventListener('touchend', handleTouchResizeEnd);
}

// Setup resize handlers for the chat-editor divider
function setupChatEditorResizeHandlers() {
    // Reset any previous event listeners
    chatEditorResize.removeEventListener('mousedown', handleChatEditorResizeStart);
    document.removeEventListener('mousemove', handleChatEditorResizeMove);
    document.removeEventListener('mouseup', handleChatEditorResizeEnd);

    // Add new event listeners
    chatEditorResize.addEventListener('mousedown', handleChatEditorResizeStart);
    document.addEventListener('mousemove', handleChatEditorResizeMove);
    document.addEventListener('mouseup', handleChatEditorResizeEnd);

    // Touch events for mobile
    chatEditorResize.addEventListener('touchstart', handleChatEditorTouchResizeStart, { passive: false });
    document.addEventListener('touchmove', handleChatEditorTouchResizeMove, { passive: false });
    document.addEventListener('touchend', handleChatEditorTouchResizeEnd);
}

// Handle resize start
function handleResizeStart(e) {
    isResizing = true;
    resizeHandle.classList.add('active');

    // Store initial positions
    const editorContainer = document.getElementById('editor-container');
    const previewContainer = document.getElementById('preview-container');
    const editorContent = document.querySelector('.editor-content');

    // Check if we're in fullscreen mode
    if (isFullscreenPreview) {
        // In fullscreen mode, always use horizontal layout
        lastEditorHeight = editorContainer.offsetWidth;
        lastPreviewHeight = previewContainer.offsetWidth;

        // Make sure the resize handle is properly styled for horizontal layout
        resizeHandle.style.width = '10px';
        resizeHandle.style.height = '100%';
        resizeHandle.style.cursor = 'col-resize';
        resizeHandle.style.position = 'absolute';
        resizeHandle.style.top = '0';
        resizeHandle.style.zIndex = '100';

        // Get the current editor percentage
        const editorPercent = (editorContainer.offsetWidth / editorContent.offsetWidth) * 100;

        // Position the resize handle
        resizeHandle.style.left = `${editorPercent}%`;
        resizeHandle.style.transform = 'translateX(-50%)';

        console.log('Resize started in fullscreen mode');
    } else {
        // Check the current layout by looking at the class
        const isCurrentlyHorizontal = codeEditorPanel.classList.contains('horizontal-layout');

        if (isCurrentlyHorizontal) {
            lastEditorHeight = editorContainer.offsetWidth;
            lastPreviewHeight = previewContainer.offsetWidth;

            // Make sure the resize handle is properly styled for horizontal layout
            resizeHandle.style.width = '6px';
            resizeHandle.style.height = '100%';
            resizeHandle.style.cursor = 'col-resize';
        } else {
            lastEditorHeight = editorContainer.offsetHeight;
            lastPreviewHeight = previewContainer.offsetHeight;

            // Make sure the resize handle is properly styled for vertical layout
            resizeHandle.style.width = '100%';
            resizeHandle.style.height = '6px';
            resizeHandle.style.cursor = 'row-resize';
        }

        // Log the current layout for debugging
        console.log('Resize started. Layout:', isCurrentlyHorizontal ? 'horizontal' : 'vertical');
    }

    // Prevent text selection during resize
    document.body.style.userSelect = 'none';
    e.preventDefault();
}

// Handle resize move
function handleResizeMove(e) {
    if (!isResizing) return;

    const editorContent = document.querySelector('.editor-content');
    const editorContainer = document.getElementById('editor-container');
    const previewContainer = document.getElementById('preview-container');
    const resizeHandle = document.getElementById('resize-handle');
    const outputContent = document.getElementById('output-content');
    const previewFrame = document.getElementById('preview-frame');

    if (!editorContainer || !previewContainer || !editorContent) {
        console.error('Missing required elements for resize');
        return;
    }

    // Check if we're in fullscreen mode
    if (isFullscreenPreview) {
        // In fullscreen mode, always use horizontal layout
        const containerRect = editorContent.getBoundingClientRect();
        const newEditorWidth = e.clientX - containerRect.left;
        const totalWidth = containerRect.width;

        // Calculate percentages (min 20%, max 80%)
        const editorPercent = Math.min(Math.max(newEditorWidth / totalWidth * 100, 20), 80);
        const previewPercent = 100 - editorPercent;

        // Apply the new widths
        editorContainer.style.width = `${editorPercent}%`;
        previewContainer.style.width = `${previewPercent}%`;

        // Ensure heights are set to 100%
        editorContainer.style.height = '100%';
        previewContainer.style.height = '100%';

        // Ensure visibility
        editorContainer.style.visibility = 'visible';
        previewContainer.style.visibility = 'visible';
        resizeHandle.style.display = 'block';

        // Position the resize handle
        resizeHandle.style.left = `${editorPercent}%`;
        resizeHandle.style.transform = 'translateX(-50%)';

        // Make sure the resize handle is properly styled
        resizeHandle.style.width = '10px';
        resizeHandle.style.height = '100%';
        resizeHandle.style.cursor = 'col-resize';
        resizeHandle.style.position = 'absolute';
        resizeHandle.style.top = '0';
        resizeHandle.style.zIndex = '100';

        // Make sure the output content takes full height in preview container
        if (outputContent) {
            outputContent.style.height = '100%';
            outputContent.style.display = 'flex';
            outputContent.style.flexDirection = 'column';
        }

        // Make sure the preview frame takes full height
        if (previewFrame) {
            previewFrame.style.height = '100%';
            previewFrame.style.minHeight = '100%';
            previewFrame.style.flex = '1';
        }

        console.log(`Fullscreen resize: Editor ${editorPercent.toFixed(1)}%, Preview ${previewPercent.toFixed(1)}%`);
    } else {
        // Check if we're in horizontal layout by checking the class
        const isCurrentlyHorizontal = codeEditorPanel.classList.contains('horizontal-layout');

        if (isCurrentlyHorizontal) {
            // Horizontal layout - adjust width
            const containerRect = editorContent.getBoundingClientRect();
            const newEditorWidth = e.clientX - containerRect.left;
            const totalWidth = containerRect.width;

            // Calculate percentages (min 20%, max 80%)
            const editorPercent = Math.min(Math.max(newEditorWidth / totalWidth * 100, 20), 80);
            const previewPercent = 100 - editorPercent;

            // Apply the new widths
            editorContainer.style.width = `${editorPercent}%`;
            previewContainer.style.width = `${previewPercent}%`;

            // Ensure heights are set to 100%
            editorContainer.style.height = '100%';
            previewContainer.style.height = '100%';

            // Ensure visibility
            editorContainer.style.visibility = 'visible';
            previewContainer.style.visibility = 'visible';
            resizeHandle.style.display = 'block';

            // Make sure the resize handle is properly styled
            if (resizeHandle) {
                resizeHandle.style.width = '6px';
                resizeHandle.style.height = '100%';
                resizeHandle.style.cursor = 'col-resize';
            }

            // Make sure the output content takes full height in preview container
            if (outputContent) {
                outputContent.style.height = '100%';
                outputContent.style.display = 'flex';
                outputContent.style.flexDirection = 'column';
            }

            console.log(`Horizontal resize: Editor ${editorPercent.toFixed(1)}%, Preview ${previewPercent.toFixed(1)}%`);
        } else {
            // Vertical layout - adjust height
            const containerRect = editorContent.getBoundingClientRect();
            const newEditorHeight = e.clientY - containerRect.top;
            const totalHeight = containerRect.height;

            // Calculate percentages (min 20%, max 80%)
            const editorPercent = Math.min(Math.max(newEditorHeight / totalHeight * 100, 20), 80);
            const previewPercent = 100 - editorPercent;

            // Apply the new heights
            editorContainer.style.height = `${editorPercent}%`;
            previewContainer.style.height = `${previewPercent}%`;

            // Ensure widths are set to 100%
            editorContainer.style.width = '100%';
            previewContainer.style.width = '100%';

            // Ensure visibility
            editorContainer.style.visibility = 'visible';
            previewContainer.style.visibility = 'visible';
            resizeHandle.style.display = 'block';

            // Make sure the resize handle is properly styled
            if (resizeHandle) {
                resizeHandle.style.width = '100%';
                resizeHandle.style.height = '6px';
                resizeHandle.style.cursor = 'row-resize';
            }

            // Make sure the output content takes full height in preview container
            if (outputContent) {
                outputContent.style.height = '100%';
                outputContent.style.display = 'flex';
                outputContent.style.flexDirection = 'column';
            }

            console.log(`Vertical resize: Editor ${editorPercent.toFixed(1)}%, Preview ${previewPercent.toFixed(1)}%`);
        }
    }

    // Force a reflow to ensure the changes take effect
    void editorContent.offsetWidth;

    // Refresh CodeMirror to ensure proper rendering
    if (codeEditor) {
        setTimeout(() => {
            codeEditor.refresh();
        }, 10);
    }

    // Run code to update the preview
    setTimeout(() => {
        runCode();
    }, 100);
}

// Handle resize end
function handleResizeEnd() {
    if (!isResizing) return;

    isResizing = false;
    resizeHandle.classList.remove('active');
    document.body.style.userSelect = '';

    // Refresh CodeMirror to ensure proper rendering
    if (codeEditor) {
        codeEditor.refresh();
    }
}

// Touch event handlers for mobile
function handleTouchResizeStart(e) {
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousedown', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    handleResizeStart(mouseEvent);
    e.preventDefault(); // Prevent scrolling
}

function handleTouchResizeMove(e) {
    if (!isResizing) return;

    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousemove', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    handleResizeMove(mouseEvent);
    e.preventDefault(); // Prevent scrolling
}

function handleTouchResizeEnd() {
    handleResizeEnd();
}

// Handle chat-editor resize start
function handleChatEditorResizeStart(e) {
    isChatEditorResizing = true;
    chatEditorResize.classList.add('active');

    // Store initial positions
    const chatDisplay = document.getElementById('chat-display');

    lastChatWidth = chatDisplay.offsetWidth;
    lastEditorWidth = codeEditorPanel.offsetWidth;

    // Prevent text selection during resize
    document.body.style.userSelect = 'none';
    e.preventDefault();
}

// Handle chat-editor resize move
function handleChatEditorResizeMove(e) {
    if (!isChatEditorResizing) return;

    const mainContent = document.querySelector('.main-content');
    const chatDisplay = document.getElementById('chat-display');
    const totalWidth = mainContent.offsetWidth;

    // Calculate new widths based on mouse position
    const newChatWidth = e.clientX - mainContent.getBoundingClientRect().left;
    const newEditorWidth = totalWidth - newChatWidth - chatEditorResize.offsetWidth;

    // Apply minimum widths (20% of total width or 200px, whichever is larger)
    const minWidth = Math.max(totalWidth * 0.2, 200);

    if (newChatWidth >= minWidth && newEditorWidth >= minWidth) {
        chatDisplay.style.width = `${newChatWidth}px`;
        codeEditorPanel.style.width = `${newEditorWidth}px`;

        // Store the new widths
        lastChatWidth = newChatWidth;
        lastEditorWidth = newEditorWidth;

        // Refresh CodeMirror to ensure proper rendering
        if (codeEditor) {
            codeEditor.refresh();
        }
    }
}

// Handle chat-editor resize end
function handleChatEditorResizeEnd() {
    if (!isChatEditorResizing) return;

    isChatEditorResizing = false;
    chatEditorResize.classList.remove('active');
    document.body.style.userSelect = '';

    // Refresh CodeMirror to ensure proper rendering
    if (codeEditor) {
        codeEditor.refresh();
    }
}

// Touch event handlers for chat-editor resize
function handleChatEditorTouchResizeStart(e) {
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousedown', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    handleChatEditorResizeStart(mouseEvent);
    e.preventDefault(); // Prevent scrolling
}

function handleChatEditorTouchResizeMove(e) {
    if (!isChatEditorResizing) return;

    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousemove', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    handleChatEditorResizeMove(mouseEvent);
    e.preventDefault(); // Prevent scrolling
}

function handleChatEditorTouchResizeEnd() {
    handleChatEditorResizeEnd();
}

// Toggle between vertical and horizontal layout function - removed as we're using horizontal layout only
/*
function toggleLayout() {
    // This function has been removed as we're now using horizontal layout only
}
*/

// Toggle fullscreen preview mode - SIMPLIFIED VERSION
function toggleFullscreenPreview() {
    isFullscreenPreview = !isFullscreenPreview;
    console.log('Toggling fullscreen mode. isFullscreenPreview:', isFullscreenPreview);

    // Get the containers
    const editorContainer = document.getElementById('editor-container');
    const previewContainer = document.getElementById('preview-container');
    const resizeHandle = document.getElementById('resize-handle');
    const editorContent = document.querySelector('.editor-content');
    const chatDisplay = document.getElementById('chat-display');

    // Save the current mode (code or preview)
    const isInCodeMode = codeEditorPanel.classList.contains('code-mode');
    const isInPreviewMode = codeEditorPanel.classList.contains('preview-mode');

    // Layout is always horizontal

    if (isFullscreenPreview) {
        console.log('Entering fullscreen mode');

        // Save current chat and editor widths before going fullscreen
        lastChatWidth = chatDisplay.offsetWidth;
        lastEditorWidth = codeEditorPanel.offsetWidth;

        // Save mode state (layout is always horizontal)
        localStorage.setItem('previous-layout', 'horizontal');
        localStorage.setItem('previous-mode', isInCodeMode ? 'code' : (isInPreviewMode ? 'preview' : 'both'));

        // Add fullscreen class
        codeEditorPanel.classList.add('fullscreen-preview');

        // Remove any existing layout classes
        codeEditorPanel.classList.remove('vertical-layout');
        codeEditorPanel.classList.remove('horizontal-layout');

        // Remove code/preview mode classes to show both panels
        codeEditorPanel.classList.remove('code-mode');
        codeEditorPanel.classList.remove('preview-mode');

        // Update button
        fullscreenPreviewButton.title = 'Exit Fullscreen';
        fullscreenPreviewButton.innerHTML = '<i class="fa-solid fa-compress"></i>';

        // Make sure chat display is not affected
        chatDisplay.style.width = '';

        // Set up the containers for side-by-side view
        editorContainer.style.width = '50%';
        previewContainer.style.width = '50%';
        editorContainer.style.height = '100%';
        previewContainer.style.height = '100%';

        // Ensure visibility
        editorContainer.style.display = 'flex';
        previewContainer.style.display = 'flex';
        editorContainer.style.visibility = 'visible';
        previewContainer.style.visibility = 'visible';

        // Configure resize handle
        if (resizeHandle) {
            resizeHandle.style.width = '6px';
            resizeHandle.style.height = '100%';
            resizeHandle.style.cursor = 'col-resize';
            resizeHandle.style.display = 'block';
        }

        // Set the flex direction of the editor content
        if (editorContent) {
            editorContent.style.flexDirection = 'row';
            editorContent.style.display = 'flex';
        }

        // Update preview
        updatePreview();

        // Re-initialize resize handlers to ensure they work in fullscreen mode
        setTimeout(() => {
            setupResizeHandlers();
        }, 100);
    } else {
        console.log('Exiting fullscreen mode');

        // Remove fullscreen class
        codeEditorPanel.classList.remove('fullscreen-preview');
        fullscreenPreviewButton.title = 'Fullscreen Preview';
        fullscreenPreviewButton.innerHTML = '<i class="fa-solid fa-expand"></i>';

        // Restore previous widths
        if (lastChatWidth && lastEditorWidth) {
            chatDisplay.style.width = `${lastChatWidth}px`;
            codeEditorPanel.style.width = `${lastEditorWidth}px`;
        }

        // Make sure resize handle is visible if editor is open
        if (isEditorVisible) {
            chatEditorResize.style.display = 'block';
        }

        // Always use horizontal layout
        isHorizontalLayout = true;
        codeEditorPanel.classList.add('horizontal-layout');
        codeEditorPanel.classList.remove('vertical-layout');

        // Restore the previous mode
        const previousMode = localStorage.getItem('previous-mode') || 'both';

        if (previousMode === 'code') {
            codeEditorPanel.classList.add('code-mode');
            codeEditorPanel.classList.remove('preview-mode');
            codeTabButton.classList.add('active');
            previewTabButton.classList.remove('active');
        } else if (previousMode === 'preview') {
            codeEditorPanel.classList.add('preview-mode');
            codeEditorPanel.classList.remove('code-mode');
            previewTabButton.classList.add('active');
            codeTabButton.classList.remove('active');
        } else {
            // Default to showing both
            codeEditorPanel.classList.remove('code-mode');
            codeEditorPanel.classList.remove('preview-mode');
        }

        // Update preview
        updatePreview();

        // Re-initialize resize handlers
        setTimeout(() => {
            setupResizeHandlers();
        }, 100);
    }

    // Refresh CodeMirror to ensure proper rendering
    if (codeEditor) {
        setTimeout(() => {
            codeEditor.refresh();
        }, 100); // Small delay to ensure DOM has updated
    }
}

// Toggle between code and preview tabs
function toggleCodeTab() {
    // If in fullscreen mode, don't change the layout
    if (isFullscreenPreview) {
        // Just update the active tab button
        codeTabButton.classList.add('active');
        previewTabButton.classList.remove('active');
        return;
    }

    // Normal mode behavior
    codeEditorPanel.classList.add('code-mode');
    codeEditorPanel.classList.remove('preview-mode');
    codeTabButton.classList.add('active');
    previewTabButton.classList.remove('active');

    // Ensure the editor container is properly displayed
    const editorContainer = document.getElementById('editor-container');
    if (editorContainer) {
        editorContainer.style.display = 'block';
    }

    // Refresh CodeMirror to ensure proper rendering
    if (codeEditor) {
        setTimeout(() => {
            codeEditor.refresh();
        }, 100); // Small delay to ensure DOM has updated
    }
}

function togglePreviewTab() {
    // If in fullscreen mode, don't change the layout
    if (isFullscreenPreview) {
        // Just update the active tab button
        previewTabButton.classList.add('active');
        codeTabButton.classList.remove('active');
        return;
    }

    // Normal mode behavior
    codeEditorPanel.classList.add('preview-mode');
    codeEditorPanel.classList.remove('code-mode');
    previewTabButton.classList.add('active');
    codeTabButton.classList.remove('active');

    // Ensure the preview container is properly displayed
    const previewContainer = document.getElementById('preview-container');
    if (previewContainer) {
        previewContainer.style.display = 'block';
    }

    // Run the code to update the preview
    runCode();
}

// Set up event listeners
function setupEventListeners() {
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Auto-resize textarea
    messageInput.addEventListener('input', autoResizeTextarea);

    // Set initial textarea height
    autoResizeTextarea();

    newChatButton.addEventListener('click', createNewChatSession);
    // Add multiple event listeners for better reliability
    stopButton.addEventListener('click', function(e) {
        console.log('Stop button CLICK event triggered');
        e.preventDefault();
        e.stopPropagation();
        stopGeneration();
    });

    stopButton.addEventListener('touchstart', function(e) {
        console.log('Stop button TOUCHSTART event triggered');
        e.preventDefault();
        e.stopPropagation();
        stopGeneration();
    });

    // Add debugging for stop button visibility
    const originalSetAttribute = stopButton.setAttribute.bind(stopButton);
    stopButton.setAttribute = function(name, value) {
        if (name === 'style' || name.includes('display')) {
            console.log('Stop button style attribute changed:', name, value);
        }
        return originalSetAttribute(name, value);
    };
    attachButton.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);
    themeToggle.addEventListener('click', toggleTheme);

    // Add reload button event listener
    const reloadButton = document.getElementById('reload-button');
    if (reloadButton) {
        reloadButton.addEventListener('click', reloadChat);
        console.log('Reload button event listener added');
    } else {
        console.error('Reload button element not found');
    }

    // Add test stop button event listener
    const testStopButton = document.getElementById('test-stop-button');
    if (testStopButton) {
        testStopButton.addEventListener('click', function() {
            console.log('🧪 Test stop button clicked');

            // Check if simulation is running
            if (stopButton.style.display === 'inline-block') {
                console.log('Test: Triggering stop function during simulation');
                stopGeneration();
                return;
            }

            // Start simulation
            console.log('Test: Starting AI generation simulation');
            window.simulateAIGeneration();

            // Run comprehensive test
            setTimeout(() => {
                window.testStopButton();
            }, 500);
        });
        console.log('Test stop button event listener added');
    }

    // Add clipboard paste functionality
    setupClipboardPaste();

    // Add global click handler to close panels when clicking outside
    setupGlobalClickHandler();

    if (mobileSidebarToggle) {
        mobileSidebarToggle.addEventListener('click', toggleSidebar);
        console.log('Mobile sidebar toggle event listener added');
    } else {
        console.error('Mobile sidebar toggle element not found');
    }

    // AI Persona and Export/Import button event listeners
    setupFeatureButtons();

    // Code editor event listeners
    codeEditorToggle.addEventListener('click', toggleCodeEditor);
    closeEditorButton.addEventListener('click', () => {
        // Reset chat display width when closing
        document.getElementById('chat-display').style.width = '';
        toggleCodeEditor();
    });
    languageSelect.addEventListener('change', (e) => changeEditorLanguage(e.target.value));
    runCodeButton.addEventListener('click', runCode);
    shareToChatButton.addEventListener('click', shareCodeToChat);
    copyCodeButton.addEventListener('click', copyCodeToClipboard);
    downloadCodeButton.addEventListener('click', downloadCode);
    clearOutputButton.addEventListener('click', clearOutput);
    fullscreenPreviewButton.addEventListener('click', toggleFullscreenPreview);
    codeTabButton.addEventListener('click', toggleCodeTab);
    previewTabButton.addEventListener('click', togglePreviewTab);

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', (e) => {
        if (window.innerWidth <= 768 &&
            sidebar.classList.contains('open') &&
            !sidebar.contains(e.target) &&
            e.target !== mobileSidebarToggle &&
            !mobileSidebarToggle.contains(e.target)) {
            console.log('Closing sidebar - clicked outside');
            closeMobileSidebar();
        }
    });

    // Close sidebar when clicking on chat area or input on mobile
    if (chatDisplay) {
        chatDisplay.addEventListener('click', () => {
            closeMobileSidebar();
        });
    }

    if (messageInput) {
        messageInput.addEventListener('focus', () => {
            closeMobileSidebar();
        });
    }

    // Close sidebar when clicking on main content area
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.addEventListener('click', (e) => {
            // Only close if clicking directly on main content, not on child elements
            if (e.target === mainContent) {
                closeMobileSidebar();
            }
        });
    }

    // Handle window resize for responsive behavior
    window.addEventListener('resize', handleWindowResize);

    initializeChatSessions();
    loadThemePreference();
}

// Handle window resize events
function handleWindowResize() {
    const isMobile = window.innerWidth <= 768;

    // If code editor is open, adjust its behavior based on screen size
    if (isEditorVisible && codeEditorPanel) {
        const editorContent = document.querySelector('.editor-content');
        if (editorContent) {
            if (isMobile) {
                editorContent.style.flexDirection = 'column';
                // Hide chat-editor resize handle on mobile
                if (chatEditorResize) {
                    chatEditorResize.style.display = 'none';
                }
                // Reset chat display width on mobile
                document.getElementById('chat-display').style.width = '';
            } else {
                editorContent.style.flexDirection = 'row';
                // Show chat-editor resize handle on desktop
                if (chatEditorResize) {
                    chatEditorResize.style.display = 'block';
                }
            }
        }

        // Refresh CodeMirror after resize
        if (codeEditor) {
            setTimeout(() => {
                codeEditor.refresh();
            }, 100);
        }
    }
}

// Set up AI Persona and Export/Import feature buttons
function setupFeatureButtons() {
    console.log('🔧 Setting up feature buttons...');

    // Wait a bit to ensure DOM is fully loaded
    setTimeout(() => {
        const personaButton = document.getElementById('ai-persona-toggle');
        const exportButton = document.getElementById('export-import-toggle');
        const personaPanel = document.getElementById('ai-persona-panel');
        const exportPanel = document.getElementById('export-import-panel');

        console.log('🔍 Feature button elements found:', {
            personaButton: !!personaButton,
            exportButton: !!exportButton,
            personaPanel: !!personaPanel,
            exportPanel: !!exportPanel
        });

        // AI Persona button with more robust event handling
        if (personaButton && personaPanel) {
            // Remove any existing event listeners first
            const newPersonaButton = personaButton.cloneNode(true);
            personaButton.parentNode.replaceChild(newPersonaButton, personaButton);

            newPersonaButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🎯 Persona button clicked!');

                // Close export panel if open
                if (exportPanel && exportPanel.classList.contains('open')) {
                    exportPanel.classList.remove('open');
                    console.log('📤 Closed export panel');
                }

                // Close mobile sidebar when opening settings
                closeMobileSidebar();

                // Toggle persona panel
                const wasOpen = personaPanel.classList.contains('open');
                personaPanel.classList.toggle('open');
                console.log(`👤 Persona panel ${wasOpen ? 'closed' : 'opened'}`);

                // Initialize AI Persona Manager if not already done
                if (!window.aiPersonaManager && typeof AIPersonaManager !== 'undefined') {
                    window.aiPersonaManager = new AIPersonaManager();
                    window.aiPersonaManager.loadSettings();
                    console.log('🚀 AI Persona Manager initialized on demand');
                }
            });
            console.log('✅ Persona button event listener added successfully');
        } else {
            console.error('❌ Persona button or panel not found:', {
                personaButton: personaButton ? 'found' : 'NOT FOUND',
                personaPanel: personaPanel ? 'found' : 'NOT FOUND'
            });
        }

        // Export/Import button with more robust event handling
        if (exportButton && exportPanel) {
            // Remove any existing event listeners first
            const newExportButton = exportButton.cloneNode(true);
            exportButton.parentNode.replaceChild(newExportButton, exportButton);

            newExportButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('📤 Export button clicked!');

                // Close persona panel if open
                if (personaPanel && personaPanel.classList.contains('open')) {
                    personaPanel.classList.remove('open');
                    console.log('👤 Closed persona panel');
                }

                // Close mobile sidebar when opening export panel
                closeMobileSidebar();

                // Toggle export panel
                const wasOpen = exportPanel.classList.contains('open');
                exportPanel.classList.toggle('open');
                console.log(`📤 Export panel ${wasOpen ? 'closed' : 'opened'}`);

                // Initialize Export/Import Manager if not already done
                if (!window.exportImportManager && typeof ExportImportManager !== 'undefined') {
                    window.exportImportManager = new ExportImportManager();
                    console.log('🚀 Export/Import Manager initialized on demand');
                }

                // Load chat selection when opening
                if (exportPanel.classList.contains('open') && window.exportImportManager) {
                    window.exportImportManager.loadChatSelection();
                    window.exportImportManager.setDefaultDateRange();
                }
            });
            console.log('✅ Export button event listener added successfully');
        } else {
            console.error('❌ Export button or panel not found:', {
                exportButton: exportButton ? 'found' : 'NOT FOUND',
                exportPanel: exportPanel ? 'found' : 'NOT FOUND'
            });
        }

        // Close buttons
        const closePersonaButton = document.getElementById('close-persona-panel');
        const closeExportButton = document.getElementById('close-export-panel');

        if (closePersonaButton && personaPanel) {
            closePersonaButton.addEventListener('click', (e) => {
                e.preventDefault();
                personaPanel.classList.remove('open');
                console.log('👤 Persona panel closed via close button');
            });
            console.log('✅ Close persona button event listener added');
        }

        if (closeExportButton && exportPanel) {
            closeExportButton.addEventListener('click', (e) => {
                e.preventDefault();
                exportPanel.classList.remove('open');
                console.log('📤 Export panel closed via close button');
            });
            console.log('✅ Close export button event listener added');
        }

        // Attribution button
        const attributionButton = document.getElementById('attribution-toggle');
        const attributionPanel = document.getElementById('attribution-panel');
        const closeAttributionButton = document.getElementById('close-attribution');

        if (attributionButton && attributionPanel) {
            attributionButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('📋 Attribution button clicked');

                // Close other panels if open
                if (personaPanel && personaPanel.classList.contains('open')) {
                    personaPanel.classList.remove('open');
                }
                if (exportPanel && exportPanel.classList.contains('open')) {
                    exportPanel.classList.remove('open');
                }

                // Close mobile sidebar when opening attribution
                closeMobileSidebar();

                // Toggle attribution panel
                attributionPanel.classList.toggle('open');
            });
            console.log('✅ Attribution button event listener added');
        }

        if (closeAttributionButton && attributionPanel) {
            closeAttributionButton.addEventListener('click', (e) => {
                e.preventDefault();
                attributionPanel.classList.remove('open');
                console.log('📋 Attribution panel closed');
            });
            console.log('✅ Close attribution button event listener added');
        }
    }, 500); // Wait 500ms to ensure DOM is ready
}

function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    files.forEach(file => {
        const supportedExtensions = ['pdf', 'txt', 'cs', 'py', 'js', 'java', 'cpp', 'c', 'h', 'html', 'css', 'php', 'rb', 'go', 'rs', 'sh', 'md', 'json', 'xml', 'yaml', 'yml', 'jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!supportedExtensions.some(ext => file.name.toLowerCase().endsWith(`.${ext}`))) {
            addMessage('system', `Unsupported file type (${file.name})`);
            return;
        }

        if (file.size > 10 * 1024 * 1024) { // 10MB limit
            addMessage('system', `File ${file.name} is too large (max 10MB)`);
            return;
        }

        if (file.type.startsWith('image/')) {
            // Handle image files with compression
            compressImageIfNeeded(file).then(compressedFile => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const fileData = {
                        name: file.name,
                        type: compressedFile.type,
                        size: compressedFile.size,
                        content: event.target.result, // Base64 encoded image
                        isImage: true
                    };
                    currentAttachments.push(fileData);
                    showFilePreview(fileData);

                    // Make sure file preview is visible
                    filePreview.style.display = 'block';
                };
                reader.readAsDataURL(compressedFile);
            }).catch(error => {
                console.error('Error compressing image:', error);
                addMessage('system', `Failed to process image: ${file.name}`);
            });
        } else if (file.name.endsWith('.pdf')) {
            // Handle PDF files
            const reader = new FileReader();
            reader.onload = async (event) => {
                try {
                    // Initialize PDF.js
                    const pdf = await pdfjsLib.getDocument(event.target.result).promise;
                    let fullText = '';

                    // Extract text from each page
                    for (let i = 1; i <= pdf.numPages; i++) {
                        const page = await pdf.getPage(i);
                        const textContent = await page.getTextContent();
                        fullText += textContent.items.map(item => item.str).join(' ');
                    }

                    const fileData = {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        content: fullText
                    };
                    currentAttachments.push(fileData);
                    showFilePreview(fileData);
                } catch (error) {
                    console.error('PDF processing error:', error);
                    addMessage('system', `Failed to process PDF: ${file.name}`);
                }
            };
            reader.readAsArrayBuffer(file);
        } else {
            // Handle all other supported files as text
            const reader = new FileReader();
            reader.onload = (event) => {
                const fileData = {
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    content: event.target.result
                };
                currentAttachments.push(fileData);
                showFilePreview(fileData);
            };
            reader.readAsText(file);
        }
    });

    filePreview.style.display = 'block';
    fileInput.value = ''; // Reset input
}

// Auto-resize textarea function
function autoResizeTextarea() {
    const textarea = messageInput;
    textarea.style.height = 'auto';
    const minHeight = 20; // Minimum height for single line
    const maxHeight = 120; // Maximum height
    const newHeight = Math.max(minHeight, Math.min(textarea.scrollHeight, maxHeight));
    textarea.style.height = newHeight + 'px';
}

// Setup clipboard paste functionality
function setupClipboardPaste() {
    // Add paste event listener only to the message input to avoid duplicates
    messageInput.addEventListener('paste', handleClipboardPaste);

    // Add visual feedback for paste operations
    const inputContainer = document.querySelector('.input-container');
    if (inputContainer) {
        inputContainer.addEventListener('dragover', (e) => {
            e.preventDefault();
            inputContainer.classList.add('drag-over');
        });

        inputContainer.addEventListener('dragleave', (e) => {
            e.preventDefault();
            inputContainer.classList.remove('drag-over');
        });

        inputContainer.addEventListener('drop', (e) => {
            e.preventDefault();
            inputContainer.classList.remove('drag-over');

            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                handleFileList(files);
            }
        });
    }
}

// Track last paste time to prevent duplicates
let lastPasteTime = 0;
const PASTE_DEBOUNCE_TIME = 500; // 500ms debounce

// Handle clipboard paste events
async function handleClipboardPaste(event) {
    const clipboardData = event.clipboardData || window.clipboardData;
    if (!clipboardData) return;

    // Prevent duplicate pastes within debounce time
    const currentTime = Date.now();
    if (currentTime - lastPasteTime < PASTE_DEBOUNCE_TIME) {
        console.log('Ignoring duplicate paste event');
        return;
    }

    const items = Array.from(clipboardData.items);
    let hasImage = false;

    // Check for images in clipboard
    for (const item of items) {
        if (item.type.startsWith('image/')) {
            hasImage = true;
            lastPasteTime = currentTime; // Update last paste time
            event.preventDefault(); // Prevent default paste behavior for images

            const file = item.getAsFile();
            if (file) {
                // Show processing indicator
                showImageProcessingIndicator(true);

                try {
                    await processClipboardImage(file);
                } catch (error) {
                    console.error('Error processing clipboard image:', error);
                    addMessage('system', 'Failed to process clipboard image');
                } finally {
                    showImageProcessingIndicator(false);
                }
            }
            break;
        }
    }

    // If no image was found, allow normal text paste
    if (!hasImage) {
        // Let the default paste behavior handle text
        return;
    }
}

// Process clipboard image
async function processClipboardImage(file) {
    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
        addMessage('system', 'Clipboard image is too large. Maximum size is 10MB.');
        return;
    }

    try {
        // Compress image if it's too large
        const compressedFile = await compressImageIfNeeded(file);

        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
                const fileData = {
                    name: `clipboard-image-${Date.now()}.png`,
                    type: compressedFile.type,
                    size: compressedFile.size,
                    content: event.target.result, // Base64 encoded image
                    isImage: true,
                    isClipboard: true
                };
                currentAttachments.push(fileData);
                showFilePreview(fileData);

                // Make sure file preview is visible
                filePreview.style.display = 'block';

                // Show success message
                addMessage('system', '📋 Image pasted from clipboard');
                resolve();
            };
            reader.onerror = reject;
            reader.readAsDataURL(compressedFile);
        });
    } catch (error) {
        console.error('Error compressing clipboard image:', error);
        addMessage('system', 'Failed to process clipboard image');
        throw error;
    }
}

// Compress image if needed to optimize for AI processing
async function compressImageIfNeeded(file, maxWidth = 1024, maxHeight = 1024, quality = 0.8) {
    return new Promise((resolve) => {
        // If file is already small enough, return as-is
        if (file.size < 1024 * 1024) { // Less than 1MB
            resolve(file);
            return;
        }

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
            // Calculate new dimensions
            let { width, height } = img;

            if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
            }

            canvas.width = width;
            canvas.height = height;

            // Draw and compress
            ctx.drawImage(img, 0, 0, width, height);

            canvas.toBlob((blob) => {
                // Create a new file with compressed data
                const compressedFile = new File([blob], file.name, {
                    type: 'image/jpeg', // Convert to JPEG for better compression
                    lastModified: Date.now()
                });

                console.log(`Image compressed: ${(file.size / 1024).toFixed(1)}KB → ${(compressedFile.size / 1024).toFixed(1)}KB`);
                resolve(compressedFile);
            }, 'image/jpeg', quality);
        };

        img.onerror = () => {
            console.warn('Failed to compress image, using original');
            resolve(file);
        };

        img.src = URL.createObjectURL(file);
    });
}

// Show/hide image processing indicator
function showImageProcessingIndicator(show) {
    let indicator = document.getElementById('image-processing-indicator');

    if (show) {
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'image-processing-indicator';
            indicator.className = 'processing-indicator';
            indicator.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> Processing image...';

            const inputContainer = document.querySelector('.input-container');
            if (inputContainer) {
                inputContainer.appendChild(indicator);
            }
        }
        indicator.style.display = 'block';
    } else {
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
}

// Handle file list (used by both file input and drag/drop)
function handleFileList(files) {
    files.forEach(file => {
        const supportedExtensions = ['pdf', 'txt', 'cs', 'py', 'js', 'java', 'cpp', 'c', 'h', 'html', 'css', 'php', 'rb', 'go', 'rs', 'sh', 'md', 'json', 'xml', 'yaml', 'yml', 'jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!supportedExtensions.some(ext => file.name.toLowerCase().endsWith(`.${ext}`))) {
            addMessage('system', `Unsupported file type (${file.name})`);
            return;
        }

        if (file.size > 10 * 1024 * 1024) { // 10MB limit
            addMessage('system', `File ${file.name} is too large (max 10MB)`);
            return;
        }

        if (file.type.startsWith('image/')) {
            // Handle image files with compression
            compressImageIfNeeded(file).then(compressedFile => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const fileData = {
                        name: file.name,
                        type: compressedFile.type,
                        size: compressedFile.size,
                        content: event.target.result, // Base64 encoded image
                        isImage: true
                    };
                    currentAttachments.push(fileData);
                    showFilePreview(fileData);

                    // Make sure file preview is visible
                    filePreview.style.display = 'block';
                };
                reader.readAsDataURL(compressedFile);
            }).catch(error => {
                console.error('Error compressing image:', error);
                addMessage('system', `Failed to process image: ${file.name}`);
            });
        } else if (file.name.endsWith('.pdf')) {
            // Handle PDF files
            const reader = new FileReader();
            reader.onload = async (event) => {
                try {
                    // Initialize PDF.js
                    const pdf = await pdfjsLib.getDocument(event.target.result).promise;
                    let fullText = '';

                    // Extract text from each page
                    for (let i = 1; i <= pdf.numPages; i++) {
                        const page = await pdf.getPage(i);
                        const textContent = await page.getTextContent();
                        fullText += textContent.items.map(item => item.str).join(' ');
                    }

                    const fileData = {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        content: fullText
                    };
                    currentAttachments.push(fileData);
                    showFilePreview(fileData);
                } catch (error) {
                    console.error('PDF processing error:', error);
                    addMessage('system', `Failed to process PDF: ${file.name}`);
                }
            };
            reader.readAsArrayBuffer(file);
        } else {
            // Handle all other supported files as text
            const reader = new FileReader();
            reader.onload = (event) => {
                const fileData = {
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    content: event.target.result
                };
                currentAttachments.push(fileData);
                showFilePreview(fileData);
            };
            reader.readAsText(file);
        }
    });

    filePreview.style.display = 'block';
}

function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
}

function showFilePreview(file) {
    const previewItem = document.createElement('div');
    previewItem.classList.add('preview-item');

    // Mark clipboard images
    if (file.isClipboard) {
        previewItem.setAttribute('data-clipboard', 'true');
    }

    // Create container for filename and buttons
    const header = document.createElement('div');
    header.style.display = 'flex';
    header.style.alignItems = 'center';
    header.style.justifyContent = 'space-between';

    // File name with icon/thumbnail
    const fileNameContainer = document.createElement('div');
    fileNameContainer.style.display = 'flex';
    fileNameContainer.style.alignItems = 'center';
    fileNameContainer.style.gap = '8px';
    fileNameContainer.style.flexGrow = '1';
    fileNameContainer.style.overflow = 'hidden';

    // Add thumbnail for images
    if (file.isImage) {
        const thumbnail = document.createElement('img');
        thumbnail.src = file.content;
        thumbnail.alt = file.name;
        thumbnail.style.width = '32px';
        thumbnail.style.height = '32px';
        thumbnail.style.objectFit = 'cover';
        thumbnail.style.borderRadius = '4px';
        thumbnail.style.border = '1px solid #ddd';
        thumbnail.style.flexShrink = '0'; // Prevent shrinking
        fileNameContainer.appendChild(thumbnail);
    }

    // File name with truncation for mobile responsiveness
    const fileName = document.createElement('span');
    let displayName = file.name;

    // Truncate filename to 20 characters for mobile responsiveness
    if (displayName.length > 20) {
        const extension = displayName.substring(displayName.lastIndexOf('.'));
        const nameWithoutExt = displayName.substring(0, displayName.lastIndexOf('.'));
        if (nameWithoutExt.length > 15) {
            displayName = nameWithoutExt.substring(0, 15) + '...' + extension;
        }
    }

    fileName.textContent = displayName;
    fileName.title = file.name; // Show full name on hover
    fileName.style.overflow = 'hidden';
    fileName.style.textOverflow = 'ellipsis';
    fileName.style.whiteSpace = 'nowrap';
    fileName.style.maxWidth = '150px'; // Ensure it doesn't exceed container
    fileNameContainer.appendChild(fileName);

    // View button
    const viewBtn = document.createElement('button');
    viewBtn.classList.add('view-preview');
    viewBtn.innerHTML = file.isImage ? '🖼️' : '👁️';
    viewBtn.title = file.isImage ? 'Toggle image preview' : 'View file content';
    viewBtn.addEventListener('click', () => {
        const contentDiv = previewItem.querySelector('.file-content');
        if (contentDiv) {
            const isVisible = contentDiv.style.display !== 'none';
            contentDiv.style.display = isVisible ? 'none' : 'block';
            viewBtn.innerHTML = file.isImage ? (isVisible ? '🖼️' : '🖼️') : '👁️';
        }
    });

    // Remove button
    const removeBtn = document.createElement('button');
    removeBtn.classList.add('remove-preview');
    removeBtn.innerHTML = '✕';
    removeBtn.title = 'Remove file';
    removeBtn.addEventListener('click', () => {
        currentAttachments = currentAttachments.filter(f => f !== file);
        previewItem.remove();
        if (currentAttachments.length === 0) {
            filePreview.style.display = 'none';
        }
    });

    header.appendChild(fileNameContainer);
    header.appendChild(viewBtn);
    header.appendChild(removeBtn);
    previewItem.appendChild(header);

    // Content area (visible by default for images, hidden for other files)
    const contentDiv = document.createElement('div');
    contentDiv.classList.add('file-content');
    contentDiv.style.display = file.isImage ? 'block' : 'none'; // Show images by default
    contentDiv.style.marginTop = '8px';
    contentDiv.style.whiteSpace = 'pre-wrap';
    contentDiv.style.maxHeight = '200px';
    contentDiv.style.overflow = 'auto';
    contentDiv.style.padding = '8px';
    contentDiv.style.backgroundColor = '#f5f5f5';
    contentDiv.style.borderRadius = '4px';

    if (file.isImage) {
        // For images, show the image preview
        const imgPreview = document.createElement('img');
        imgPreview.src = file.content;
        imgPreview.alt = file.name;
        imgPreview.style.maxWidth = '100%';
        imgPreview.style.maxHeight = '180px';
        imgPreview.style.display = 'block';
        imgPreview.style.margin = '0 auto';
        contentDiv.appendChild(imgPreview);
    } else if (file.type === 'text/plain' || file.name.match(/\.(cs|py|js|java|cpp|c|h|html|css|php|rb|go|rs|sh|md|json|xml|yaml|yml)$/i)) {
        contentDiv.textContent = file.content.length > 1000 ?
            file.content.substring(0, 1000) + '...' :
            file.content;
    } else {
        contentDiv.textContent = file.content;
    }

    previewItem.appendChild(contentDiv);
    filePreview.appendChild(previewItem);
}

function stopGeneration() {
    console.log('Stop button clicked - attempting to stop generation');

    if (abortController) {
        console.log('Aborting fetch request...');
        abortController.abort();
        abortController = null;
    }

    // Also abort any Android streaming requests
    if (typeof window.abortAllStreams === 'function') {
        console.log('Aborting Android streams...');
        window.abortAllStreams();
    }

    stopButton.style.display = 'none';
    addMessage('system', 'Generation stopped by user');
    console.log('Stop generation completed');
}

// Send message to Ollama
async function sendMessage() {
    console.log('🚀 sendMessage() called');

    const message = messageInput.value.trim();
    console.log('📝 Message:', message);
    if (!message) {
        console.log('❌ Empty message, returning early');
        return;
    }

    // Skip demo mode - proceed with actual AI API calls even in file:// protocol
    // This allows the Android WebView to make proper API calls

    const currentProvider = window.aiPersonaManager?.currentProvider || 'ollama';
    console.log('🔧 Current provider:', currentProvider);

    console.log('🤖 Current model:', currentModel);
    if (!currentModel) {
        console.log('❌ No model selected, returning early');
        addMessage('system', 'Please select a model first');
        return;
    }

    // Check if API key is required and available
    console.log('🔑 Checking API key requirements...');
    if (window.aiProviderManager.requiresApiKey(currentProvider)) {
        const apiKey = window.aiPersonaManager?.apiKeys[currentProvider];
        console.log('🔑 API key required, checking availability:', !!apiKey);
        if (!apiKey) {
            console.log('❌ No API key found, returning early');
            addMessage('system', `Please configure your API key for ${window.aiProviderManager.getProviderDisplayName(currentProvider)} in the AI Persona Settings.`);
            return;
        }
    } else {
        console.log('🔑 No API key required for this provider');
    }

    console.log('✅ All checks passed, proceeding with message sending...');

    // Add user message to display
    const messageContainer = document.createElement('div');
    messageContainer.classList.add('message-container');

    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message', 'user-message');
    messageDiv.textContent = message;

    const copyButton = document.createElement('button');
    copyButton.classList.add('copy-button');
    copyButton.title = 'Copy to clipboard';
    copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>';
    copyButton.addEventListener('click', () => {
        navigator.clipboard.writeText(messageDiv.textContent)
            .then(() => {
                copyButton.innerHTML = '<i class="fa-solid fa-check"></i>';
                setTimeout(() => copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
            })
            .catch(err => {
                console.error('Failed to copy:', err);
                copyButton.innerHTML = '<i class="fa-solid fa-xmark"></i>';
                setTimeout(() => copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
            });
    });

    messageContainer.appendChild(copyButton);
    messageContainer.appendChild(messageDiv);
    chatMessages.appendChild(messageContainer);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Clear the input field
    messageInput.value = '';

    // Show stop button and create new AbortController
    console.log('🛑 About to show stop button...');
    console.log('🛑 Stop button element:', stopButton);
    console.log('🛑 Stop button current display:', stopButton.style.display);

    stopButton.style.display = 'inline-block';
    console.log('🛑 Stop button display set to inline-block');
    console.log('🛑 Stop button computed style:', window.getComputedStyle(stopButton).display);
    console.log('🛑 Stop button visibility:', window.getComputedStyle(stopButton).visibility);
    console.log('🛑 Stop button opacity:', window.getComputedStyle(stopButton).opacity);

    abortController = new AbortController();
    console.log('🎛️ AbortController created:', abortController);

    try {
            // NEW CHAT HISTORY SYSTEM - Ensure we have a current session
            if (!currentChatSession.id) {
                createNewChatSession();
            }

            // Create a copy of the current session messages
            const messages = [...currentChatSession.messages];

            // Add system prompt if AI persona manager is available
            if (window.aiPersonaManager) {
                const systemPrompt = window.aiPersonaManager.getSystemPrompt();
                if (systemPrompt.trim()) {
                    // Check if a system message already exists
                    const systemMessageExists = messages.some(msg => msg.role === 'system');
                    if (!systemMessageExists) {
                        messages.unshift({ role: 'system', content: systemPrompt });
                    }
                }
            }

            // Add the current user message to session
            const userMessage = { role: 'user', content: message };
            currentChatSession.messages.push(userMessage);

            // Use the updated messages for the API request
            messages.push(userMessage);

            // Add any new attachments with provider-specific formatting
            currentAttachments.forEach(file => {
                if (file.isImage) {
                    // Extract base64 data without data URL prefix
                    const base64Data = file.content.split(',')[1];

                    // Create image message with proper formatting for all providers
                    const imageMessage = {
                        role: 'user',
                        content: `[IMAGE: ${file.name}]`,
                        images: [base64Data]
                    };

                    // Add image type information for better provider compatibility
                    if (file.type) {
                        imageMessage.imageType = file.type;
                    }

                    messages.push(imageMessage);
                } else {
                    messages.push({
                        role: 'user',
                        content: `[FILE: ${file.name}]\n${file.content}`
                    });
                }
            });

            // NEW CHAT HISTORY SYSTEM - Messages are already in currentChatSession.messages
            // No need to reassign as messages are already tracked in the session

        // Use the new provider system
        const apiKey = window.aiPersonaManager?.apiKeys[currentProvider];
        let assistantMessage = '';

        // Create a wrapper for both thinking and assistant message
        const assistantResponseWrapper = document.createElement('div');
        assistantResponseWrapper.classList.add('assistant-response-wrapper');

        // Create assistant message container for streaming
        const assistantMessageContainer = document.createElement('div');
        assistantMessageContainer.classList.add('message-container');

        // Add avatar for assistant messages
        const avatar = document.createElement('div');
        avatar.classList.add('message-avatar', 'assistant-avatar');
        const avatarImg = document.createElement('img');
        avatarImg.src = 'icons/ai_chat_profile.svg';
        avatarImg.alt = 'AI';
        avatar.appendChild(avatarImg);
        assistantMessageContainer.appendChild(avatar);

        const assistantMessageDiv = document.createElement('div');
        assistantMessageDiv.classList.add('message', 'assistant-message');
        assistantMessageDiv.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
        assistantMessageContainer.appendChild(assistantMessageDiv);

        // Add the message container to the wrapper
        assistantResponseWrapper.appendChild(assistantMessageContainer);

        // Add the wrapper to chat messages
        chatMessages.appendChild(assistantResponseWrapper);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Create variables to track thinking content separately
        let thinkingContent = '';
        let thinkingDiv = null;
        let lastProgressTime = Date.now();
        let streamStallTimeout = null;
        let isThinkingPhase = false;

        // Function to handle stream stall detection
        const resetStreamStallTimer = () => {
            lastProgressTime = Date.now();
            if (streamStallTimeout) {
                clearTimeout(streamStallTimeout);
            }
            // Set a 30-second timeout for stream stalls
            streamStallTimeout = setTimeout(() => {
                console.warn('⚠️ Stream appears to be stalled, attempting to continue...');
                handleStreamStall();
            }, 30000);
        };

        const handleStreamStall = () => {
            console.log('🔄 Handling stream stall...');

            // If we're in thinking phase and have some content, try to continue
            if (isThinkingPhase && thinkingContent.length > 0) {
                console.log('🧠 Stream stalled during thinking phase, attempting continuation...');

                // Update thinking header to show stall
                if (thinkingDiv) {
                    const header = thinkingDiv.querySelector('.thinking-header span');
                    if (header) {
                        header.textContent = 'AI thinking interrupted - attempting to continue...';
                    }
                }

                // Try to continue the generation
                setTimeout(() => {
                    window.continueStalliedGeneration();
                }, 1000);
            } else if (assistantMessage.length === 0) {
                console.log('📝 Stream stalled with no content, showing continue option...');
                // Show continue button if no content received
                showStreamStallContinueOption();
            }
        };

        // Create a progress callback for streaming with reasoning model support
        const onProgress = (data) => {
            console.log('📥 Received streaming data:', typeof data, data);
            resetStreamStallTimer(); // Reset stall timer on any progress

            // Handle both old string format and new object format
            if (typeof data === 'string') {
                // Legacy format - just add to assistant message
                assistantMessage += data;
                isThinkingPhase = false;
                console.log('📝 Added to assistant message (legacy):', data.substring(0, 50) + '...');
            } else if (typeof data === 'object') {
                // New format for reasoning models
                if (data.type === 'thinking') {
                    // Handle thinking content
                    thinkingContent += data.content;
                    isThinkingPhase = true;
                    console.log('🧠 Added thinking content:', data.content.substring(0, 50) + '...');

                    // Create or update thinking display
                    if (!thinkingDiv) {
                        thinkingDiv = document.createElement('div');
                        thinkingDiv.className = 'thinking-content';
                        thinkingDiv.innerHTML = `
                            <div class="thinking-header">
                                <i class="fa-solid fa-brain"></i>
                                <span>AI is thinking...</span>
                                <button class="thinking-toggle" onclick="this.parentElement.parentElement.classList.toggle('collapsed')">
                                    <i class="fa-solid fa-chevron-down"></i>
                                </button>
                            </div>
                            <div class="thinking-body">
                                <pre class="thinking-text"></pre>
                            </div>
                        `;
                        // Insert thinking content before the message container in the wrapper
                        assistantResponseWrapper.insertBefore(thinkingDiv, assistantMessageContainer);
                    }

                    // Update thinking content
                    const thinkingTextElement = thinkingDiv.querySelector('.thinking-text');
                    if (thinkingTextElement) {
                        thinkingTextElement.textContent = thinkingContent;
                    }
                } else if (data.type === 'content') {
                    // Handle regular content
                    assistantMessage += data.content;
                    isThinkingPhase = false;
                    console.log('📝 Added to assistant message (content):', data.content.substring(0, 50) + '...');
                } else if (data.type === 'finish') {
                    // Handle completion
                    console.log('✅ Reasoning model finished');
                    isThinkingPhase = false;
                    if (streamStallTimeout) {
                        clearTimeout(streamStallTimeout);
                        streamStallTimeout = null;
                    }
                    if (thinkingDiv) {
                        const header = thinkingDiv.querySelector('.thinking-header span');
                        if (header) {
                            header.textContent = 'AI finished thinking';
                        }
                    }
                }
            }

            // Update the assistant message display in real-time
            assistantMessageDiv.innerHTML = marked.parse(assistantMessage);
            // Re-highlight code blocks
            assistantMessageDiv.querySelectorAll('pre code').forEach(block => {
                hljs.highlightElement(block);
            });
            // Wrap tables for mobile responsiveness
            assistantMessageDiv.querySelectorAll('table').forEach(table => {
                if (!table.parentElement.classList.contains('table-wrapper')) {
                    const wrapper = document.createElement('div');
                    wrapper.classList.add('table-wrapper');
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });
            chatMessages.scrollTop = chatMessages.scrollHeight;
        };

        // Functions to handle stalled generation
        window.continueStalliedGeneration = async () => {
            try {
                console.log('🔄 Attempting to continue stalled generation...');

                // Create a continuation prompt that includes the thinking so far
                const continuationPrompt = `Please continue your response. You were in the middle of thinking about this problem. Here's what you were thinking so far:\n\n${thinkingContent}\n\nPlease continue from where you left off and provide the complete answer.`;

                // Add the continuation prompt to messages
                const continuationMessages = [
                    ...messages,
                    { role: 'user', content: continuationPrompt }
                ];

                // Reset variables for continuation
                let continuationContent = '';

                const continuationProgress = (data) => {
                    if (typeof data === 'string') {
                        continuationContent += data;
                        assistantMessage += data;
                    } else if (typeof data === 'object' && data.type === 'content') {
                        continuationContent += data.content;
                        assistantMessage += data.content;
                    }

                    // Update display
                    assistantMessageDiv.innerHTML = marked.parse(assistantMessage);
                    assistantMessageDiv.querySelectorAll('pre code').forEach(block => {
                        hljs.highlightElement(block);
                    });
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                };

                // Continue generation
                await window.aiProviderManager.sendMessage(
                    currentProvider,
                    currentModel,
                    continuationMessages,
                    apiKey,
                    continuationProgress,
                    abortController.signal
                );

                console.log('✅ Successfully continued stalled generation');

            } catch (error) {
                console.error('❌ Failed to continue stalled generation:', error);
                showStreamStallContinueOption();
            }
        };

        const showStreamStallContinueOption = () => {
            // Add a continue button for stalled streams
            const stallMessage = document.createElement('div');
            stallMessage.className = 'stream-stall-message';
            stallMessage.innerHTML = `
                <div class="stall-notice">
                    <i class="fa-solid fa-exclamation-triangle"></i>
                    <span>Stream appears to be interrupted. The AI was thinking but didn't complete the response.</span>
                </div>
                <button class="continue-stall-button" onclick="this.parentElement.remove(); window.continueStalliedGeneration();">
                    <i class="fa-solid fa-play"></i> Continue Generation
                </button>
            `;
            assistantMessageContainer.appendChild(stallMessage);
        };

        // Start the stall detection timer
        resetStreamStallTimer();

        // Send message using the provider manager with enhanced error handling
        try {
            // Show processing indicator if images are present
            const hasImages = currentAttachments.some(file => file.isImage);
            if (hasImages) {
                showImageProcessingIndicator(true);
                addMessage('system', `🖼️ Processing ${currentAttachments.filter(f => f.isImage).length} image(s) with ${window.aiProviderManager.getProviderDisplayName(currentProvider)}...`);
            }

            assistantMessage = await window.aiProviderManager.sendMessage(
                currentProvider,
                currentModel,
                messages,
                apiKey,
                onProgress,
                abortController.signal
            );

            if (hasImages) {
                showImageProcessingIndicator(false);
            }
        } catch (imageError) {
            showImageProcessingIndicator(false);

            // Handle specific image processing errors
            if (imageError.message.includes('timeout') || imageError.message.includes('Timeout')) {
                throw new Error(`Image processing timed out. Try using a smaller image or a different provider.`);
            } else if (imageError.message.includes('unsupported') || imageError.message.includes('format')) {
                throw new Error(`Unsupported image format. Please use JPEG, PNG, or WebP images.`);
            } else if (imageError.message.includes('size') || imageError.message.includes('large')) {
                throw new Error(`Image too large. Please use images smaller than 10MB.`);
            } else {
                throw imageError; // Re-throw other errors
            }
        }

        // Clear attachments after sending
        currentAttachments = [];
        filePreview.innerHTML = '';
        filePreview.style.display = 'none';

        // The streaming is now handled by the provider manager
        // Final message processing - preserve thinking content and update the existing message
        console.log('🏁 Final processing - assistantMessage length:', assistantMessage.length);
        console.log('🏁 Final assistantMessage content:', assistantMessage.substring(0, 100) + '...');

        // Don't remove the streaming container - instead update it to final state
        // Remove typing indicator and update with final content
        assistantMessageDiv.innerHTML = marked.parse(assistantMessage);

        // Re-highlight code blocks in final message
        assistantMessageDiv.querySelectorAll('pre code').forEach(block => {
            hljs.highlightElement(block);
        });

        // Update thinking header to show completion if thinking content exists
        if (thinkingDiv) {
            const header = thinkingDiv.querySelector('.thinking-header span');
            if (header) {
                header.textContent = 'AI finished thinking';
            }
        }

        // Use the existing container as the final container
        const finalMessageContainer = assistantMessageContainer;

        // The avatar already exists, no need to create a new one
        // The assistantMessageDiv already exists and has been updated with final content
        const finalMessage = assistantMessageDiv;

        // Add code block buttons (copy, edit) to the final message
        finalMessage.querySelectorAll('pre code').forEach(block => {
            hljs.highlightElement(block);

            // Create button container for code blocks
            const codeContainer = block.parentElement;
            const buttonContainer = document.createElement('div');
            buttonContainer.classList.add('code-button-container');
            buttonContainer.style.position = 'absolute';
            buttonContainer.style.top = '5px';
            buttonContainer.style.right = '5px';
            buttonContainer.style.display = 'flex';
            buttonContainer.style.gap = '5px';

            // Copy button
            const codeCopyBtn = document.createElement('button');
            codeCopyBtn.classList.add('code-copy-button');
            codeCopyBtn.title = 'Copy code';
            codeCopyBtn.innerHTML = '<i class="fa-regular fa-copy"></i>';
            codeCopyBtn.addEventListener('click', () => {
                navigator.clipboard.writeText(block.textContent)
                    .then(() => {
                        codeCopyBtn.innerHTML = '<i class="fa-solid fa-check"></i>';
                        setTimeout(() => codeCopyBtn.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy code:', err);
                        codeCopyBtn.innerHTML = '<i class="fa-solid fa-xmark"></i>';
                        setTimeout(() => codeCopyBtn.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
                    });
            });

            // Edit in code editor button
            const editBtn = document.createElement('button');
            editBtn.classList.add('code-copy-button');
            editBtn.title = 'Edit in code editor';
            editBtn.innerHTML = '<i class="fa-solid fa-code"></i>';
            editBtn.addEventListener('click', () => {
                // Detect language from class
                let language = 'javascript'; // Default
                const classNames = block.className.split(' ');
                for (const className of classNames) {
                    if (className.startsWith('language-')) {
                        const detectedLang = className.replace('language-', '');
                        // Map highlight.js language to CodeMirror mode
                        switch (detectedLang) {
                            case 'js':
                            case 'javascript':
                                language = 'javascript';
                                break;
                            case 'py':
                            case 'python':
                                language = 'python';
                                break;
                            case 'html':
                                language = 'htmlmixed';
                                // Check if the code contains both HTML and CSS
                                if (block.textContent.includes('<style>') ||
                                    block.textContent.includes('<link rel="stylesheet"')) {
                                    language = 'htmlcss';
                                }
                                break;
                            case 'css':
                                language = 'css';
                                break;
                            case 'xml':
                                language = 'xml';
                                break;
                            default:
                                language = 'javascript';
                        }
                        break;
                    }
                }

                // Open code editor
                if (!isEditorVisible) {
                    toggleCodeEditor();
                }

                // Set language
                languageSelect.value = language;
                changeEditorLanguage(language);

                // Set code
                if (codeEditor) {
                    codeEditor.setValue(block.textContent);
                    codeEditor.refresh();
                }
            });

            buttonContainer.appendChild(codeCopyBtn);
            buttonContainer.appendChild(editBtn);

            // Add the button container to the code container
            codeContainer.style.position = 'relative';
            codeContainer.appendChild(buttonContainer);
        });

        // Add copy button for the entire message
        const copyButton = document.createElement('button');
        copyButton.classList.add('copy-button');
        copyButton.title = 'Copy to clipboard';
        copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>';
        copyButton.addEventListener('click', () => {
            navigator.clipboard.writeText(assistantMessage)
                .then(() => {
                    copyButton.innerHTML = '<i class="fa-solid fa-check"></i>';
                    setTimeout(() => copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
                })
                .catch(err => {
                    console.error('Failed to copy:', err);
                    copyButton.innerHTML = '<i class="fa-solid fa-xmark"></i>';
                    setTimeout(() => copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
                });
        });

        // Check if the response appears incomplete and add continue button
        const continueButton = createContinueButtonIfNeeded(assistantMessage, assistantMessageDiv);

        // Since we're reusing the existing container, we need to update the message div
        // The finalMessage is actually the assistantMessageDiv we already have
        assistantMessageDiv.appendChild(copyButton);
        if (continueButton) {
            assistantMessageDiv.appendChild(continueButton);
        }
        // No need to append to chatMessages again - it's already there

        // NEW CHAT HISTORY SYSTEM - Add assistant message to current session
        currentChatSession.messages.push({ role: 'assistant', content: assistantMessage });

        // Save the updated chat session
        saveChatSession();
    } catch (error) {
        console.error('Error sending message:', error);
        if (error.name === 'AbortError') {
            addMessage('system', 'Generation stopped');
        } else {
            const providerName = window.aiProviderManager.getProviderDisplayName(currentProvider);
            addMessage('system', `Error communicating with ${providerName}: ${error.message}`);
        }
    } finally {
        console.log('Hiding stop button in finally block');
        stopButton.style.display = 'none';
        abortController = null;

        // Clean up stall timer
        if (streamStallTimeout) {
            clearTimeout(streamStallTimeout);
            streamStallTimeout = null;
        }

        console.log('AbortController and timers cleared');
    }
}

// Function to detect incomplete responses and create continue button
function createContinueButtonIfNeeded(assistantMessage, messageElement) {
    // Patterns that suggest incomplete responses
    const incompletePatterns = [
        /```[^`]*$/,  // Unclosed code block
        /\.\.\.$/, // Ends with ellipsis
        /\s+$/, // Ends abruptly with whitespace
        /<[^>]*$/, // Unclosed HTML tag
        /\([^)]*$/, // Unclosed parenthesis
        /\{[^}]*$/, // Unclosed brace
        /\[[^\]]*$/, // Unclosed bracket
        /function\s+\w+\s*\([^)]*\)\s*\{[^}]*$/m, // Incomplete function
        /class\s+\w+[^{]*\{[^}]*$/m, // Incomplete class
        /if\s*\([^)]*\)\s*\{[^}]*$/m, // Incomplete if statement
    ];

    // Check if message appears incomplete
    const isIncomplete = incompletePatterns.some(pattern => pattern.test(assistantMessage.trim()));

    // Also check if the message is suspiciously short for a code generation request
    const messageLength = assistantMessage.trim().length;
    const hasCodeRequest = messageElement.querySelector('pre code') !== null;
    const isSuspiciouslyShort = hasCodeRequest && messageLength < 100;

    if (isIncomplete || isSuspiciouslyShort) {
        const continueButton = document.createElement('button');
        continueButton.classList.add('continue-button');
        continueButton.title = 'Continue generation from where it left off';
        continueButton.innerHTML = '<i class="fa-solid fa-play"></i> Continue';

        continueButton.addEventListener('click', () => {
            continueGeneration(assistantMessage, messageElement, continueButton);
        });

        return continueButton;
    }

    return null;
}

// Function to continue generation from where it left off
async function continueGeneration(previousMessage, messageElement, continueButton) {
    console.log('🔄 Continue button clicked - starting generation continuation...');
    console.log('📝 Previous message length:', previousMessage.length);
    console.log('🎯 Current provider:', localStorage.getItem('selectedProvider'));

    // Disable the continue button
    continueButton.disabled = true;
    continueButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> Continuing...';

    try {
        // Get current settings using the same method as sendMessage
        const currentProvider = localStorage.getItem('selectedProvider') || 'openai';
        const currentModel = localStorage.getItem(`selectedModel_${currentProvider}`) || '';

        // Use the same API key retrieval method as the main sendMessage function
        let apiKey = '';
        if (window.aiPersonaManager && window.aiPersonaManager.apiKeys) {
            apiKey = window.aiPersonaManager.apiKeys[currentProvider] || '';
        }

        // Fallback to localStorage if persona manager doesn't have the key
        if (!apiKey) {
            apiKey = localStorage.getItem(`${currentProvider}_api_key`) || '';
        }

        console.log('🔑 API key found:', !!apiKey);
        console.log('🤖 Model:', currentModel);

        if (!currentModel) {
            throw new Error('No model selected');
        }

        if (!apiKey) {
            throw new Error(`No API key found for ${currentProvider}`);
        }

        // Create a more specific continuation prompt that includes context
        const continuationPrompt = `Please continue from where you left off. Here's what you were generating:

${previousMessage.slice(-500)}...

Please continue and complete this response. Don't repeat what was already generated, just continue from where it stopped.`;

        // Prepare messages for continuation - use the same format as sendMessage
        const messages = [
            ...currentChatSession.messages,
            { role: 'user', content: continuationPrompt }
        ];

        console.log('📨 Prepared', messages.length, 'messages for continuation');

        // Show stop button
        const stopButton = document.getElementById('stop-button');
        if (stopButton) {
            stopButton.style.display = 'inline-block';
            console.log('🛑 Stop button shown');
        }

        // Create new abort controller - make sure to clear any existing one
        if (abortController) {
            console.log('⚠️ Clearing existing abort controller');
            abortController.abort();
        }
        abortController = new AbortController();
        console.log('🎮 New abort controller created');

        let continuedContent = '';

        // Create progress callback for continuation with enhanced logging
        const onProgress = (data) => {
            console.log('📥 Continue progress data:', typeof data, data);

            // Handle both old string format and new object format
            if (typeof data === 'string') {
                continuedContent += data;
                console.log('📝 Added string content, total length:', continuedContent.length);
            } else if (typeof data === 'object') {
                if (data.type === 'content') {
                    continuedContent += data.content;
                    console.log('📝 Added object content, total length:', continuedContent.length);
                } else if (data.type === 'thinking') {
                    console.log('🧠 Thinking content in continuation (ignoring):', data.content.substring(0, 50) + '...');
                    // Don't add thinking content to the continued response
                    return;
                }
            }

            // Update the message display in real-time
            const updatedContent = previousMessage + continuedContent;
            messageElement.innerHTML = marked.parse(updatedContent);

            // Re-highlight code blocks
            messageElement.querySelectorAll('pre code').forEach(block => {
                hljs.highlightElement(block);
            });

            // Scroll to bottom
            const chatMessages = document.getElementById('chat-messages');
            if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        };

        console.log('🚀 Sending continuation request...');

        // Send continuation request
        await window.aiProviderManager.sendMessage(
            currentProvider,
            currentModel,
            messages,
            apiKey,
            onProgress,
            abortController.signal
        );

        console.log('✅ Continuation request completed');
        console.log('📊 Continued content length:', continuedContent.length);
        console.log('📊 Full response length:', (previousMessage + continuedContent).length);

        // Update the message content
        const finalContent = previousMessage + continuedContent;
        messageElement.innerHTML = marked.parse(finalContent);

        // Re-highlight code blocks
        messageElement.querySelectorAll('pre code').forEach(block => {
            hljs.highlightElement(block);
        });

        // Update chat session with the continued content
        if (currentChatSession && currentChatSession.messages && currentChatSession.messages.length > 0) {
            const lastMessage = currentChatSession.messages[currentChatSession.messages.length - 1];
            if (lastMessage.role === 'assistant') {
                console.log('💾 Updating last assistant message with continued content');
                lastMessage.content = finalContent;
            }
        }

        // Add the continuation prompt to chat history
        if (currentChatSession && currentChatSession.messages) {
            currentChatSession.messages.push({ role: 'user', content: continuationPrompt });
            currentChatSession.messages.push({ role: 'assistant', content: continuedContent });
            console.log('💾 Added continuation to chat history');
        }

        // Save updated session
        if (typeof saveChatSession === 'function') {
            saveChatSession();
            console.log('💾 Chat session saved');
        }

        // Remove the continue button
        continueButton.remove();
        console.log('🗑️ Continue button removed');

        // Check if we need another continue button
        const newContinueButton = createContinueButtonIfNeeded(finalContent, messageElement);
        if (newContinueButton) {
            messageElement.appendChild(newContinueButton);
            console.log('🔄 New continue button added');
        } else {
            console.log('✅ Response appears complete, no new continue button needed');
        }

    } catch (error) {
        console.error('❌ Error continuing generation:', error);
        console.error('❌ Error details:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });

        // Reset continue button
        continueButton.disabled = false;
        continueButton.innerHTML = '<i class="fa-solid fa-play"></i> Continue';

        if (error.name === 'AbortError') {
            console.log('🛑 Continuation stopped by user');
            addMessage('system', 'Continuation stopped');
        } else {
            const providerName = window.aiProviderManager ?
                window.aiProviderManager.getProviderDisplayName(currentProvider) :
                currentProvider;
            const errorMessage = `Error continuing generation with ${providerName}: ${error.message}`;
            console.error('❌ Showing error message:', errorMessage);
            addMessage('system', errorMessage);
        }
    } finally {
        console.log('🧹 Cleaning up continue generation...');

        // Hide stop button
        const stopButton = document.getElementById('stop-button');
        if (stopButton) {
            stopButton.style.display = 'none';
            console.log('🛑 Stop button hidden');
        }

        // Clear abort controller
        if (abortController) {
            abortController = null;
            console.log('🎮 Abort controller cleared');
        }
    }
}

// Add message to chat display
function addMessage(role, content, skipSave = false) {
    const messageContainer = document.createElement('div');
    messageContainer.classList.add('message-container');

    // Add avatar for assistant messages
    if (role === 'assistant') {
        const avatar = document.createElement('div');
        avatar.classList.add('message-avatar', 'assistant-avatar');
        const avatarImg = document.createElement('img');
        avatarImg.src = 'icons/ai_chat_profile.svg';
        avatarImg.alt = 'AI';
        avatar.appendChild(avatarImg);
        messageContainer.appendChild(avatar);
    }

    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message', `${role}-message`, 'formatted-message');

    if (role === 'system') {
        // System messages are styled differently
        messageDiv.classList.add('system-message');
        messageDiv.textContent = content;
    } else if (role === 'assistant') {
        // Convert markdown to HTML for assistant messages
        messageDiv.innerHTML = marked.parse(content);

        // Add syntax highlighting to code blocks
        messageDiv.querySelectorAll('pre code').forEach(block => {
            hljs.highlightElement(block);

            // Create button container for code blocks
            const codeContainer = block.parentElement;
            const buttonContainer = document.createElement('div');
            buttonContainer.classList.add('code-button-container');
            buttonContainer.style.position = 'absolute';
            buttonContainer.style.top = '5px';
            buttonContainer.style.right = '5px';
            buttonContainer.style.display = 'flex';
            buttonContainer.style.gap = '5px';

            // Copy button
            const codeCopyBtn = document.createElement('button');
            codeCopyBtn.classList.add('code-copy-button');
            codeCopyBtn.title = 'Copy code';
            codeCopyBtn.innerHTML = '<i class="fa-regular fa-copy"></i>';
            codeCopyBtn.addEventListener('click', () => {
                navigator.clipboard.writeText(block.textContent)
                    .then(() => {
                        codeCopyBtn.innerHTML = '<i class="fa-solid fa-check"></i>';
                        setTimeout(() => codeCopyBtn.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy code:', err);
                        codeCopyBtn.innerHTML = '<i class="fa-solid fa-xmark"></i>';
                        setTimeout(() => codeCopyBtn.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
                    });
            });

            // Edit in code editor button
            const editBtn = document.createElement('button');
            editBtn.classList.add('code-copy-button');
            editBtn.title = 'Edit in code editor';
            editBtn.innerHTML = '<i class="fa-solid fa-code"></i>';
            editBtn.addEventListener('click', () => {
                // Detect language from class
                let language = 'javascript'; // Default
                const classNames = block.className.split(' ');
                for (const className of classNames) {
                    if (className.startsWith('language-')) {
                        const detectedLang = className.replace('language-', '');
                        // Map highlight.js language to CodeMirror mode
                        switch (detectedLang) {
                            case 'js':
                            case 'javascript':
                                language = 'javascript';
                                break;
                            case 'py':
                            case 'python':
                                language = 'python';
                                break;
                            case 'html':
                                language = 'htmlmixed';
                                // Check if the code contains both HTML and CSS
                                if (block.textContent.includes('<style>') ||
                                    block.textContent.includes('<link rel="stylesheet"')) {
                                    language = 'htmlcss';
                                }
                                break;
                            case 'css':
                                language = 'css';
                                break;
                            case 'xml':
                                language = 'xml';
                                break;
                            default:
                                language = 'javascript';
                        }
                        break;
                    }
                }

                // Open code editor
                if (!isEditorVisible) {
                    toggleCodeEditor();
                }

                // Set language
                languageSelect.value = language;
                changeEditorLanguage(language);

                // Set code
                if (codeEditor) {
                    codeEditor.setValue(block.textContent);
                    codeEditor.refresh();
                }
            });

            buttonContainer.appendChild(codeCopyBtn);
            buttonContainer.appendChild(editBtn);

            // Add the button container to the code container
            codeContainer.style.position = 'relative';
            codeContainer.appendChild(buttonContainer);
        });

        // Wrap tables for mobile responsiveness
        messageDiv.querySelectorAll('table').forEach(table => {
            if (!table.parentElement.classList.contains('table-wrapper')) {
                const wrapper = document.createElement('div');
                wrapper.classList.add('table-wrapper');
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });
    } else {
        // User messages
        messageDiv.textContent = content;
    }

    // Add copy button for all non-system messages
    if (role !== 'system') {
        const copyButton = document.createElement('button');
        copyButton.classList.add('copy-button');
        copyButton.title = 'Copy to clipboard';
        copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>';
        copyButton.addEventListener('click', () => {
            const textToCopy = role === 'assistant' ? content : messageDiv.textContent;
            navigator.clipboard.writeText(textToCopy)
                .then(() => {
                    copyButton.innerHTML = '<i class="fa-solid fa-check"></i>';
                    setTimeout(() => copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
                })
                .catch(err => {
                    console.error('Failed to copy:', err);
                    copyButton.innerHTML = '<i class="fa-solid fa-xmark"></i>';
                    setTimeout(() => copyButton.innerHTML = '<i class="fa-regular fa-copy"></i>', 2000);
                });
        });

        messageDiv.appendChild(copyButton);
    }

    messageContainer.appendChild(messageDiv);

    // Add avatar for user messages (after message div)
    if (role === 'user') {
        const avatar = document.createElement('div');
        avatar.classList.add('message-avatar', 'user-avatar');
        const avatarImg = document.createElement('img');
        avatarImg.src = 'icons/user_chat_profile.svg';
        avatarImg.alt = 'User';
        avatar.appendChild(avatarImg);
        messageContainer.appendChild(avatar);
        messageContainer.classList.add('user-message-container');
    }

    chatMessages.appendChild(messageContainer);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // NEW CHAT HISTORY SYSTEM - Add to current session and save
    if ((role === 'user' || role === 'assistant') && !skipSave) {
        // Ensure we have a current session
        if (!currentChatSession.id) {
            createNewChatSession();
        }

        // Add message to current session
        currentChatSession.messages.push({ role, content });

        // Save the session
        saveChatSession();
    }
}

// Initialize highlight.js
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing highlight.js');
    hljs.highlightAll();
});

// Expose functions and variables globally for other modules
window.renderChatSessionsList = renderChatSessionsList;
window.changeEditorLanguage = changeEditorLanguage;
window.generateChatTitle = generateChatTitle;
window.loadModels = loadModels;
window.currentChatSession = currentChatSession;
window.continueGeneration = continueGeneration;
window.createContinueButtonIfNeeded = createContinueButtonIfNeeded;

// Debug function to test continue button functionality
window.testContinueButton = function() {
    console.log('🧪 === CONTINUE BUTTON TEST ===');

    // Find all continue buttons
    const continueButtons = document.querySelectorAll('.continue-button');
    console.log('🔍 Found', continueButtons.length, 'continue buttons');

    if (continueButtons.length === 0) {
        console.log('⚠️ No continue buttons found. Creating a test scenario...');

        // Find the last assistant message
        const assistantMessages = document.querySelectorAll('.message.assistant .message-content');
        if (assistantMessages.length > 0) {
            const lastMessage = assistantMessages[assistantMessages.length - 1];
            const messageText = lastMessage.textContent || lastMessage.innerText;
            console.log('📝 Last message length:', messageText.length);

            // Force create a continue button for testing
            const testButton = createContinueButtonIfNeeded(messageText, lastMessage);
            if (testButton) {
                lastMessage.appendChild(testButton);
                console.log('✅ Test continue button created');
            } else {
                console.log('❌ Could not create test continue button');
            }
        }
    } else {
        // Test existing continue buttons
        continueButtons.forEach((button, index) => {
            console.log(`🔘 Testing continue button ${index + 1}:`);
            console.log('   Disabled:', button.disabled);
            console.log('   Text:', button.textContent);
            console.log('   Click handler:', !!button.onclick);

            // Check if button is clickable
            if (!button.disabled) {
                console.log('   ✅ Button appears clickable');
            } else {
                console.log('   ⚠️ Button is disabled');
            }
        });
    }

    // Check current chat session
    console.log('💬 Current chat session:', !!currentChatSession);
    if (currentChatSession) {
        console.log('   Messages count:', currentChatSession.messages?.length || 0);
    }

    // Check API settings
    const provider = localStorage.getItem('selectedProvider');
    const model = localStorage.getItem(`selectedModel_${provider}`);
    console.log('🤖 Current provider:', provider);
    console.log('🤖 Current model:', model);

    // Check API key
    let hasApiKey = false;
    if (window.aiPersonaManager && window.aiPersonaManager.apiKeys) {
        hasApiKey = !!window.aiPersonaManager.apiKeys[provider];
    }
    if (!hasApiKey) {
        hasApiKey = !!localStorage.getItem(`${provider}_api_key`);
    }
    console.log('🔑 Has API key:', hasApiKey);

    return {
        continueButtonsFound: continueButtons.length,
        hasApiKey,
        provider,
        model,
        chatSessionExists: !!currentChatSession
    };
};

// Comprehensive stop button test function
window.testStopButton = function() {
    console.log('🧪 === STOP BUTTON COMPREHENSIVE TEST ===');

    // 1. Check DOM element
    console.log('1️⃣ DOM Element Check:');
    const stopBtn = document.getElementById('stop-button');
    console.log('   Stop button element:', stopBtn);
    console.log('   Element exists:', !!stopBtn);
    if (!stopBtn) {
        console.error('   ❌ CRITICAL: Stop button not found in DOM!');
        return false;
    }

    // 2. Check CSS styles
    console.log('2️⃣ CSS Styles Check:');
    const computedStyle = window.getComputedStyle(stopBtn);
    console.log('   Current display:', stopBtn.style.display);
    console.log('   Computed display:', computedStyle.display);
    console.log('   Computed visibility:', computedStyle.visibility);
    console.log('   Computed opacity:', computedStyle.opacity);
    console.log('   Computed z-index:', computedStyle.zIndex);
    console.log('   Computed position:', computedStyle.position);

    // 3. Check event listeners
    console.log('3️⃣ Event Listeners Check:');
    console.log('   Click event listener attached:', !!stopBtn.onclick || stopBtn.addEventListener);

    // 4. Test visibility toggle
    console.log('4️⃣ Visibility Toggle Test:');
    const originalDisplay = stopBtn.style.display;
    stopBtn.style.display = 'inline-block';
    console.log('   After setting inline-block:', window.getComputedStyle(stopBtn).display);
    stopBtn.style.display = 'none';
    console.log('   After setting none:', window.getComputedStyle(stopBtn).display);
    stopBtn.style.display = originalDisplay;

    // 5. Check required variables
    console.log('5️⃣ Required Variables Check:');
    console.log('   currentModel:', currentModel);
    console.log('   window.aiProviderManager:', !!window.aiProviderManager);
    console.log('   window.aiPersonaManager:', !!window.aiPersonaManager);
    console.log('   abortController:', abortController);

    // 6. Test stop function
    console.log('6️⃣ Stop Function Test:');
    try {
        if (typeof stopGeneration === 'function') {
            console.log('   stopGeneration function exists: ✅');
        } else {
            console.error('   stopGeneration function missing: ❌');
        }
    } catch (e) {
        console.error('   Error testing stopGeneration:', e);
    }

    console.log('🧪 === TEST COMPLETE ===');
    return true;
};

// Simulate AI generation for testing stop button
window.simulateAIGeneration = function() {
    console.log('🤖 Starting simulated AI generation...');

    // Show stop button
    stopButton.style.display = 'inline-block';
    abortController = new AbortController();

    // Add a fake assistant message
    const messageContainer = document.createElement('div');
    messageContainer.classList.add('message-container');

    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message', 'assistant-message');
    messageDiv.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';

    messageContainer.appendChild(messageDiv);
    chatMessages.appendChild(messageContainer);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    let text = '';
    const words = ['This', 'is', 'a', 'simulated', 'AI', 'response', 'for', 'testing', 'the', 'stop', 'button', 'functionality.', 'You', 'should', 'be', 'able', 'to', 'click', 'the', 'stop', 'button', 'to', 'interrupt', 'this', 'generation.'];
    let wordIndex = 0;

    const interval = setInterval(() => {
        if (abortController && abortController.signal.aborted) {
            console.log('🛑 Simulation aborted by stop button');
            clearInterval(interval);
            messageDiv.innerHTML = text + ' <em>[Generation stopped by user]</em>';
            stopButton.style.display = 'none';
            return;
        }

        if (wordIndex < words.length) {
            text += (wordIndex > 0 ? ' ' : '') + words[wordIndex];
            messageDiv.innerHTML = text + '<span class="cursor">|</span>';
            wordIndex++;
            chatMessages.scrollTop = chatMessages.scrollHeight;
        } else {
            clearInterval(interval);
            messageDiv.innerHTML = text;
            stopButton.style.display = 'none';
            abortController = null;
            console.log('🤖 Simulation completed');
        }
    }, 200);

    // Auto-stop after 10 seconds if not manually stopped
    setTimeout(() => {
        if (interval) {
            clearInterval(interval);
            if (abortController) {
                abortController.abort();
            }
        }
    }, 10000);
};

// Auto-run test after page load
setTimeout(() => {
    console.log('🚀 Auto-running stop button test...');
    window.testStopButton();
}, 2000);



// Set up event listeners for module functionality
function setupModuleEventListeners() {
    console.log('Setting up module event listeners...');

    // AI Persona settings
    if (window.aiPersonaManager) {
        console.log('Setting up AI Persona event listeners...');
        const personaSelect = document.getElementById('persona-select');
        const responseStyleSelect = document.getElementById('response-style');
        const customPromptTextarea = document.getElementById('custom-prompt');
        const savePersonaButton = document.getElementById('save-persona');
        const resetPersonaButton = document.getElementById('reset-persona');

        if (personaSelect) {
            personaSelect.addEventListener('change', (e) => {
                window.aiPersonaManager.currentPersona = e.target.value;
                window.aiPersonaManager.updateCustomPromptVisibility();
                window.aiPersonaManager.updateUI();
            });
            console.log('Persona select event listener added');
        }

        if (responseStyleSelect) {
            responseStyleSelect.addEventListener('change', (e) => {
                window.aiPersonaManager.responseStyle = e.target.value;
            });
            console.log('Response style select event listener added');
        }

        if (customPromptTextarea) {
            customPromptTextarea.addEventListener('input', (e) => {
                window.aiPersonaManager.customPrompt = e.target.value;
            });
            console.log('Custom prompt textarea event listener added');
        }

        if (savePersonaButton) {
            savePersonaButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Save persona button clicked');
                window.aiPersonaManager.saveSettings();
                window.aiPersonaManager.showNotification('Settings saved!');
            });
            console.log('Save persona button event listener added');
        }

        if (resetPersonaButton) {
            resetPersonaButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Reset persona button clicked');
                window.aiPersonaManager.resetToDefault();
                window.aiPersonaManager.showNotification('Settings reset!');
            });
            console.log('Reset persona button event listener added');
        }

        // Expertise checkboxes
        const expertiseCheckboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]');
        expertiseCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                window.aiPersonaManager.updateExpertiseAreas();
            });
        });
        console.log(`Added event listeners to ${expertiseCheckboxes.length} expertise checkboxes`);
    } else {
        console.warn('AI Persona Manager not available for event listener setup');
    }

    // Export/Import functionality
    if (window.exportImportManager) {
        console.log('Setting up Export/Import event listeners...');
        const exportSelected = document.getElementById('export-selected');
        const exportAll = document.getElementById('export-all');
        const importChats = document.getElementById('import-chats');

        if (exportSelected) {
            exportSelected.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Export selected button clicked');
                window.exportImportManager.exportSelectedChats();
            });
            console.log('Export selected button event listener added');
        }

        if (exportAll) {
            exportAll.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Export all button clicked');
                window.exportImportManager.exportAllChats();
            });
            console.log('Export all button event listener added');
        }

        if (importChats) {
            importChats.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Import chats button clicked');
                window.exportImportManager.importChats();
            });
            console.log('Import chats button event listener added');
        }
    } else {
        console.warn('Export/Import Manager not available for event listener setup');
    }

    // Code Integration functionality
    if (window.codeIntegrationManager) {
        console.log('Setting up Code Integration event listeners...');
        window.codeIntegrationManager.initializeEventListeners();
        console.log('Code Integration event listeners initialized');
    } else {
        console.warn('Code Integration Manager not available for event listener setup');
    }
}

// Start the app
document.addEventListener('DOMContentLoaded', () => {
    console.log('Main script initializing...');
    init();
});
