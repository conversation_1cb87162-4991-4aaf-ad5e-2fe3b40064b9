<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Chat History System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #2a2a2a;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #28a745;
            color: white;
        }
        .error {
            background-color: #dc3545;
            color: white;
        }
        .info {
            background-color: #17a2b8;
            color: white;
        }
        #test-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #333;
            padding: 10px;
            background-color: #1e1e1e;
        }
        .storage-info {
            background-color: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>New Chat History System Test Suite</h1>
    <p>This page tests the completely rewritten chat history system to ensure it works reliably.</p>

    <div class="test-section">
        <h2>Storage Information</h2>
        <div id="storage-info" class="storage-info"></div>
        <button class="test-button" onclick="showStorageInfo()">Refresh Storage Info</button>
        <button class="test-button" onclick="clearAllStorage()">Clear All Storage</button>
    </div>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        <button class="test-button" onclick="createTestData()">Create Test Data</button>
    </div>

    <div class="test-section">
        <h2>Individual Tests</h2>
        <button class="test-button" onclick="testNewChatSystem()">Test New Chat System</button>
        <button class="test-button" onclick="testChatSaving()">Test Chat Saving</button>
        <button class="test-button" onclick="testChatLoading()">Test Chat Loading</button>
        <button class="test-button" onclick="testChatIsolation()">Test Chat Isolation</button>
        <button class="test-button" onclick="testDataStructure()">Test Data Structure</button>
        <button class="test-button" onclick="testExportImport()">Test Export/Import</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <script>
        const STORAGE_KEY = 'ai_chat_sessions_v2';

        // Test utilities
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(`TEST: ${message}`);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function showStorageInfo() {
            const storageDiv = document.getElementById('storage-info');
            const oldData = localStorage.getItem('ollama-chats');
            const newData = localStorage.getItem(STORAGE_KEY);
            
            let info = '<h3>Storage Status:</h3>';
            info += `<p><strong>Old System (ollama-chats):</strong> ${oldData ? 'EXISTS' : 'NOT FOUND'}</p>`;
            info += `<p><strong>New System (${STORAGE_KEY}):</strong> ${newData ? 'EXISTS' : 'NOT FOUND'}</p>`;
            
            if (newData) {
                try {
                    const sessions = JSON.parse(newData);
                    info += `<p><strong>Sessions Count:</strong> ${sessions.length}</p>`;
                    info += `<p><strong>Storage Size:</strong> ${(newData.length / 1024).toFixed(2)} KB</p>`;
                } catch (error) {
                    info += `<p><strong>Error:</strong> Invalid JSON data</p>`;
                }
            }
            
            storageDiv.innerHTML = info;
        }

        function clearAllStorage() {
            localStorage.removeItem('ollama-chats');
            localStorage.removeItem(STORAGE_KEY);
            showStorageInfo();
            logResult('All storage cleared', 'info');
        }

        function createTestData() {
            const testSessions = [
                {
                    id: 'test_session_1',
                    title: 'Test Chat 1',
                    messages: [
                        { role: 'user', content: 'Hello from test chat 1' },
                        { role: 'assistant', content: 'Hi! This is response from test chat 1.' }
                    ],
                    model: 'test-model',
                    timestamp: Date.now() - 3600000
                },
                {
                    id: 'test_session_2',
                    title: 'Test Chat 2',
                    messages: [
                        { role: 'user', content: 'Hello from test chat 2' },
                        { role: 'assistant', content: 'Hi! This is response from test chat 2.' }
                    ],
                    model: 'test-model',
                    timestamp: Date.now() - 1800000
                }
            ];

            localStorage.setItem(STORAGE_KEY, JSON.stringify(testSessions));
            showStorageInfo();
            logResult('Test data created with 2 sample sessions', 'success');
        }

        function testNewChatSystem() {
            logResult('Testing New Chat System...', 'info');
            
            try {
                // Test data structure
                const testSession = {
                    id: 'test_' + Date.now(),
                    title: 'New System Test',
                    messages: [],
                    model: 'test-model',
                    timestamp: Date.now()
                };

                // Test if structure is valid
                if (testSession.id && testSession.messages && Array.isArray(testSession.messages)) {
                    logResult('✓ New chat session structure is valid', 'success');
                } else {
                    logResult('✗ New chat session structure is invalid', 'error');
                    return;
                }

                logResult('✓ New Chat System test completed', 'success');
            } catch (error) {
                logResult(`✗ New Chat System test failed: ${error.message}`, 'error');
            }
        }

        function testChatSaving() {
            logResult('Testing Chat Saving...', 'info');
            
            try {
                const testSession = {
                    id: 'save_test_' + Date.now(),
                    title: 'Save Test Chat',
                    messages: [
                        { role: 'user', content: 'Test save message' },
                        { role: 'assistant', content: 'Test save response' }
                    ],
                    model: 'test-model',
                    timestamp: Date.now()
                };

                // Get existing sessions
                const existingSessions = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                
                // Add new session
                existingSessions.push(testSession);
                
                // Save to localStorage
                localStorage.setItem(STORAGE_KEY, JSON.stringify(existingSessions));

                // Verify save
                const savedSessions = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                const foundSession = savedSessions.find(s => s.id === testSession.id);
                
                if (foundSession && foundSession.messages.length === 2) {
                    logResult('✓ Chat saving works correctly', 'success');
                } else {
                    logResult('✗ Chat saving failed', 'error');
                }
            } catch (error) {
                logResult(`✗ Chat saving test failed: ${error.message}`, 'error');
            }
        }

        function testChatLoading() {
            logResult('Testing Chat Loading...', 'info');
            
            try {
                const savedSessions = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                
                if (savedSessions.length > 0) {
                    const testSession = savedSessions[0];
                    
                    // Test loading simulation
                    const loadedSession = { ...testSession };
                    
                    if (loadedSession.id === testSession.id && 
                        loadedSession.messages.length === testSession.messages.length) {
                        logResult('✓ Chat loading works correctly', 'success');
                    } else {
                        logResult('✗ Chat loading failed', 'error');
                    }
                } else {
                    logResult('! No sessions found to test loading', 'info');
                }
            } catch (error) {
                logResult(`✗ Chat loading test failed: ${error.message}`, 'error');
            }
        }

        function testChatIsolation() {
            logResult('Testing Chat Isolation...', 'info');
            
            try {
                const savedSessions = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                
                if (savedSessions.length >= 2) {
                    const session1 = savedSessions[0];
                    const session2 = savedSessions[1];
                    
                    // Test that sessions have different IDs and content
                    if (session1.id !== session2.id && 
                        session1.title !== session2.title) {
                        logResult('✓ Chat isolation works correctly', 'success');
                    } else {
                        logResult('✗ Chat isolation failed - sessions are not properly isolated', 'error');
                    }
                } else {
                    logResult('! Need at least 2 sessions to test isolation', 'info');
                }
            } catch (error) {
                logResult(`✗ Chat isolation test failed: ${error.message}`, 'error');
            }
        }

        function testDataStructure() {
            logResult('Testing Data Structure...', 'info');
            
            try {
                const savedSessions = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                
                if (savedSessions.length > 0) {
                    let validSessions = 0;
                    
                    savedSessions.forEach(session => {
                        if (session.id && 
                            session.title && 
                            Array.isArray(session.messages) && 
                            session.timestamp) {
                            validSessions++;
                        }
                    });
                    
                    if (validSessions === savedSessions.length) {
                        logResult('✓ All sessions have valid data structure', 'success');
                    } else {
                        logResult(`✗ ${savedSessions.length - validSessions} sessions have invalid structure`, 'error');
                    }
                } else {
                    logResult('! No sessions found to test structure', 'info');
                }
            } catch (error) {
                logResult(`✗ Data structure test failed: ${error.message}`, 'error');
            }
        }

        function testExportImport() {
            logResult('Testing Export/Import Compatibility...', 'info');
            
            try {
                const savedSessions = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                
                if (savedSessions.length > 0) {
                    // Test export format
                    const exportData = {
                        exportDate: new Date().toISOString(),
                        chatCount: savedSessions.length,
                        chats: savedSessions
                    };

                    // Test if export data is valid JSON
                    const exportJson = JSON.stringify(exportData);
                    const parsedData = JSON.parse(exportJson);
                    
                    if (parsedData.chats && Array.isArray(parsedData.chats)) {
                        logResult('✓ Export/Import format is compatible', 'success');
                    } else {
                        logResult('✗ Export/Import format is invalid', 'error');
                    }
                } else {
                    logResult('! No sessions found to test export/import', 'info');
                }
            } catch (error) {
                logResult(`✗ Export/Import test failed: ${error.message}`, 'error');
            }
        }

        function runAllTests() {
            logResult('=== Starting New Chat History System Tests ===', 'info');
            clearResults();
            
            setTimeout(() => testNewChatSystem(), 100);
            setTimeout(() => testChatSaving(), 200);
            setTimeout(() => testChatLoading(), 300);
            setTimeout(() => testChatIsolation(), 400);
            setTimeout(() => testDataStructure(), 500);
            setTimeout(() => testExportImport(), 600);
            
            setTimeout(() => {
                logResult('=== All Tests Completed ===', 'info');
                showStorageInfo();
            }, 700);
        }

        // Initialize on load
        window.addEventListener('load', () => {
            logResult('New Chat History System Test Suite Loaded', 'info');
            showStorageInfo();
        });
    </script>
</body>
</html>
